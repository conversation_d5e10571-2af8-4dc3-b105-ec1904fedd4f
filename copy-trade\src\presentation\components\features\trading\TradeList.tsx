'use client';

import { TradeResponseDTO } from '../../../application/dto/trading/TradeResponseDTO';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';

interface TradeListProps {
  trades: TradeResponseDTO[];
  onExecuteTrade?: (tradeId: string) => Promise<void>;
  onCancelTrade?: (tradeId: string) => Promise<void>;
  isLoading?: boolean;
}

export function TradeList({ trades, onExecuteTrade, onCancelTrade, isLoading = false }: TradeListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(price);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  if (trades.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">No trades found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {trades.map((trade) => (
        <Card key={trade.id}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">
                  {trade.symbol} - {trade.exchange}
                </CardTitle>
                <CardDescription>
                  {trade.transactionType} {trade.quantity} shares
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                {trade.isDemo && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    DEMO
                  </Badge>
                )}
                <Badge className={getStatusColor(trade.status)}>
                  {trade.status}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Order Type</p>
                <p className="font-medium">{trade.orderType}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Product Type</p>
                <p className="font-medium">{trade.productType}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Price</p>
                <p className="font-medium">{formatPrice(trade.price)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Value</p>
                <p className="font-medium">{formatPrice(trade.price * trade.quantity)}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Created At</p>
                <p className="font-medium">{formatDate(trade.createdAt)}</p>
              </div>
              {trade.executedAt && (
                <div>
                  <p className="text-sm text-gray-500">Executed At</p>
                  <p className="font-medium">{formatDate(trade.executedAt)}</p>
                </div>
              )}
            </div>

            {trade.zerodhaOrderId && (
              <div className="mb-4">
                <p className="text-sm text-gray-500">Zerodha Order ID</p>
                <p className="font-medium font-mono text-sm">{trade.zerodhaOrderId}</p>
              </div>
            )}

            {trade.status === 'PENDING' && (
              <div className="flex space-x-2">
                {onExecuteTrade && (
                  <Button
                    onClick={() => onExecuteTrade(trade.id)}
                    disabled={isLoading}
                    size="sm"
                  >
                    Execute
                  </Button>
                )}
                {onCancelTrade && (
                  <Button
                    onClick={() => onCancelTrade(trade.id)}
                    disabled={isLoading}
                    variant="outline"
                    size="sm"
                  >
                    Cancel
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
