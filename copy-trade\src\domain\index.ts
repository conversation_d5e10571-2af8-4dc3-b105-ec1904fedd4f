// Trading Domain Exports
export { Trade } from './trading/entities/Trade';
export { TradeId } from './trading/value-objects/TradeId';
export { Symbol } from './trading/value-objects/Symbol';
export { Price } from './trading/value-objects/Price';
export { Quantity } from './trading/value-objects/Quantity';
export { TradeRepository } from './trading/repositories/TradeRepository';
export { TradingDomainService } from './trading/services/TradingDomainService';
export { TradeStatus, OrderType, TransactionType, ProductType, Exchange } from './trading/enums/TradeEnums';
export * from './trading/events/TradeEvents';
export * from './trading/exceptions/TradingExceptions';

// User Domain Exports
export { User } from './user/entities/User';
export { UserId } from './user/value-objects/UserId';
export { Email } from './user/value-objects/Email';
export { UserRepository } from './user/repositories/UserRepository';
export { UserRole, UserStatus } from './user/enums/UserEnums';

// Copy Trading Domain Exports
export { MasterChildRelationship } from './copy-trading/entities/MasterChildRelationship';
export { TradeCopy } from './copy-trading/entities/TradeCopy';
export { RelationshipId } from './copy-trading/value-objects/RelationshipId';
export { TradeCopyId } from './copy-trading/value-objects/TradeCopyId';
export { MasterChildRelationshipRepository } from './copy-trading/repositories/MasterChildRelationshipRepository';
export { TradeCopyRepository } from './copy-trading/repositories/TradeCopyRepository';
export { CopyTradingDomainService } from './copy-trading/services/CopyTradingDomainService';
export { CopyStatus, RelationshipStatus } from './copy-trading/enums/CopyTradingEnums';

// Shared Domain Exports
export { Money } from './shared/value-objects/Money';
export * from './shared/exceptions/DomainException';
export * from './shared/types/index';
