import { PrismaClient } from '@prisma/client';
import { TradeCopy } from '../../../domain/copy-trading/entities/TradeCopy';
import { TradeCopyRepository } from '../../../domain/copy-trading/repositories/TradeCopyRepository';
import { TradeCopyId } from '../../../domain/copy-trading/value-objects/TradeCopyId';
import { TradeId } from '../../../domain/trading/value-objects/TradeId';
import { UserId } from '../../../domain/user/value-objects/UserId';

export class PrismaTradeCopyRepository implements TradeCopyRepository {
  constructor(private readonly prisma: PrismaClient) {}

  public async save(tradeCopy: TradeCopy): Promise<void> {
    const data = tradeCopy.toPersistence();
    await this.prisma.tradeCopy.create({
      data: {
        id: data.id,
        originalTradeId: data.originalTradeId,
        masterId: data.masterId,
        childId: data.childId,
        copiedTradeId: data.copiedTradeId,
        status: data.status,
        errorMessage: data.errorMessage,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt
      }
    });
  }

  public async findById(id: TradeCopyId): Promise<TradeCopy | null> {
    const tradeCopy = await this.prisma.tradeCopy.findUnique({
      where: { id: id.value }
    });

    if (!tradeCopy) {
      return null;
    }

    return TradeCopy.fromPersistence({
      id: tradeCopy.id,
      originalTradeId: tradeCopy.originalTradeId,
      masterId: tradeCopy.masterId,
      childId: tradeCopy.childId,
      copiedTradeId: tradeCopy.copiedTradeId,
      status: tradeCopy.status,
      errorMessage: tradeCopy.errorMessage || undefined,
      createdAt: tradeCopy.createdAt,
      updatedAt: tradeCopy.updatedAt
    });
  }

  public async findByOriginalTradeId(originalTradeId: TradeId): Promise<TradeCopy[]> {
    const tradeCopies = await this.prisma.tradeCopy.findMany({
      where: { originalTradeId: originalTradeId.value },
      orderBy: { createdAt: 'desc' }
    });

    return tradeCopies.map(tradeCopy => TradeCopy.fromPersistence({
      id: tradeCopy.id,
      originalTradeId: tradeCopy.originalTradeId,
      masterId: tradeCopy.masterId,
      childId: tradeCopy.childId,
      copiedTradeId: tradeCopy.copiedTradeId,
      status: tradeCopy.status,
      errorMessage: tradeCopy.errorMessage || undefined,
      createdAt: tradeCopy.createdAt,
      updatedAt: tradeCopy.updatedAt
    }));
  }

  public async findByMasterId(masterId: UserId): Promise<TradeCopy[]> {
    const tradeCopies = await this.prisma.tradeCopy.findMany({
      where: { masterId: masterId.value },
      orderBy: { createdAt: 'desc' }
    });

    return tradeCopies.map(tradeCopy => TradeCopy.fromPersistence({
      id: tradeCopy.id,
      originalTradeId: tradeCopy.originalTradeId,
      masterId: tradeCopy.masterId,
      childId: tradeCopy.childId,
      copiedTradeId: tradeCopy.copiedTradeId,
      status: tradeCopy.status,
      errorMessage: tradeCopy.errorMessage || undefined,
      createdAt: tradeCopy.createdAt,
      updatedAt: tradeCopy.updatedAt
    }));
  }

  public async findByChildId(childId: UserId): Promise<TradeCopy[]> {
    const tradeCopies = await this.prisma.tradeCopy.findMany({
      where: { childId: childId.value },
      orderBy: { createdAt: 'desc' }
    });

    return tradeCopies.map(tradeCopy => TradeCopy.fromPersistence({
      id: tradeCopy.id,
      originalTradeId: tradeCopy.originalTradeId,
      masterId: tradeCopy.masterId,
      childId: tradeCopy.childId,
      copiedTradeId: tradeCopy.copiedTradeId,
      status: tradeCopy.status,
      errorMessage: tradeCopy.errorMessage || undefined,
      createdAt: tradeCopy.createdAt,
      updatedAt: tradeCopy.updatedAt
    }));
  }

  public async findPendingCopies(): Promise<TradeCopy[]> {
    const tradeCopies = await this.prisma.tradeCopy.findMany({
      where: { status: 'PENDING' },
      orderBy: { createdAt: 'asc' }
    });

    return tradeCopies.map(tradeCopy => TradeCopy.fromPersistence({
      id: tradeCopy.id,
      originalTradeId: tradeCopy.originalTradeId,
      masterId: tradeCopy.masterId,
      childId: tradeCopy.childId,
      copiedTradeId: tradeCopy.copiedTradeId,
      status: tradeCopy.status,
      errorMessage: tradeCopy.errorMessage || undefined,
      createdAt: tradeCopy.createdAt,
      updatedAt: tradeCopy.updatedAt
    }));
  }

  public async update(tradeCopy: TradeCopy): Promise<void> {
    const data = tradeCopy.toPersistence();
    await this.prisma.tradeCopy.update({
      where: { id: data.id },
      data: {
        status: data.status,
        errorMessage: data.errorMessage,
        updatedAt: data.updatedAt
      }
    });
  }

  public async delete(id: TradeCopyId): Promise<void> {
    await this.prisma.tradeCopy.delete({
      where: { id: id.value }
    });
  }
}
