import { v4 as uuidv4 } from 'uuid';

export class TradeId {
  private constructor(private readonly _value: string) {
    if (!_value || _value.trim().length === 0) {
      throw new Error('TradeId cannot be empty');
    }
  }

  public static generate(): TradeId {
    return new TradeId(uuidv4());
  }

  public static fromString(value: string): TradeId {
    return new TradeId(value);
  }

  public get value(): string {
    return this._value;
  }

  public equals(other: TradeId): boolean {
    return this._value === other._value;
  }

  public toString(): string {
    return this._value;
  }
}
