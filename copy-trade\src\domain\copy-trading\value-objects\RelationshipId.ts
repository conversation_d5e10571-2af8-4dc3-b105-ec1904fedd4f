import { v4 as uuidv4 } from 'uuid';

export class RelationshipId {
  private constructor(private readonly _value: string) {
    if (!_value || _value.trim().length === 0) {
      throw new Error('RelationshipId cannot be empty');
    }
  }

  public static generate(): RelationshipId {
    return new RelationshipId(uuidv4());
  }

  public static fromString(value: string): RelationshipId {
    return new RelationshipId(value);
  }

  public get value(): string {
    return this._value;
  }

  public equals(other: RelationshipId): boolean {
    return this._value === other._value;
  }

  public toString(): string {
    return this._value;
  }
}
