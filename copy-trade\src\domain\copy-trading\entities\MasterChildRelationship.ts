import { RelationshipId } from '../value-objects/RelationshipId';
import { UserId } from '../../user/value-objects/UserId';

export class MasterChildRelationship {
  private constructor(
    private readonly _id: RelationshipId,
    private readonly _masterId: UserId,
    private readonly _childId: UserId,
    private _isActive: boolean = true,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date(),
    private _deletedAt?: Date
  ) {}

  public static create(params: {
    id: RelationshipId;
    masterId: UserId;
    childId: UserId;
  }): MasterChildRelationship {
    return new MasterChildRelationship(
      params.id,
      params.masterId,
      params.childId
    );
  }

  public static fromPersistence(data: {
    id: string;
    masterId: string;
    childId: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
  }): MasterChildRelationship {
    return new MasterChildRelationship(
      RelationshipId.fromString(data.id),
      UserId.fromString(data.masterId),
      UserId.fromString(data.childId),
      data.isActive,
      data.createdAt,
      data.updatedAt,
      data.deletedAt
    );
  }

  // Getters
  public get id(): RelationshipId { return this._id; }
  public get masterId(): UserId { return this._masterId; }
  public get childId(): UserId { return this._childId; }
  public get isActive(): boolean { return this._isActive; }
  public get createdAt(): Date { return this._createdAt; }
  public get updatedAt(): Date { return this._updatedAt; }
  public get deletedAt(): Date | undefined { return this._deletedAt; }

  // Business methods
  public activate(): void {
    if (this._deletedAt) {
      throw new Error('Cannot activate a deleted relationship');
    }
    this._isActive = true;
    this._updatedAt = new Date();
  }

  public deactivate(): void {
    this._isActive = false;
    this._updatedAt = new Date();
  }

  public delete(): void {
    this._isActive = false;
    this._deletedAt = new Date();
    this._updatedAt = new Date();
  }

  public isDeleted(): boolean {
    return this._deletedAt !== undefined;
  }

  public canCopyTrades(): boolean {
    return this._isActive && !this.isDeleted();
  }

  public toPersistence(): any {
    return {
      id: this._id.value,
      masterId: this._masterId.value,
      childId: this._childId.value,
      isActive: this._isActive,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt,
      deletedAt: this._deletedAt
    };
  }
}
