# Migration Guide: From Legacy to Enterprise Architecture

## Overview

This guide helps you migrate from the legacy copy-trade application structure to the new enterprise-level Domain-Driven Design (DDD) and Hexagonal Architecture.

## Migration Steps

### Phase 1: Understanding the New Structure ✅ COMPLETED

The new enterprise structure has been created with the following layers:

```
src/
├── domain/              # Business logic and rules
├── application/         # Use cases and orchestration
├── infrastructure/      # Technical implementations
├── presentation/        # UI components and interactions
└── shared/             # Common utilities and configuration
```

### Phase 2: Key Files Created ✅ COMPLETED

#### Domain Layer
- ✅ `src/domain/trading/entities/Trade.ts` - Rich trade entity with business logic
- ✅ `src/domain/trading/value-objects/` - TradeId, Symbol, Price, Quantity
- ✅ `src/domain/trading/repositories/TradeRepository.ts` - Repository interface
- ✅ `src/domain/trading/services/TradingDomainService.ts` - Business rules
- ✅ `src/domain/user/entities/User.ts` - User entity
- ✅ `src/domain/copy-trading/entities/` - Relationship and TradeCopy entities

#### Application Layer
- ✅ `src/application/use-cases/trading/` - CreateTrade, ExecuteTrade use cases
- ✅ `src/application/dto/` - Data transfer objects
- ✅ `src/application/mappers/` - Domain to DTO mapping

#### Infrastructure Layer
- ✅ `src/infrastructure/database/repositories/` - Prisma implementations
- ✅ `src/infrastructure/brokers/zerodha/ZerodhaAdapter.ts` - Broker integration

#### Presentation Layer
- ✅ `src/presentation/components/features/` - Feature-specific components
- ✅ `src/presentation/hooks/` - Custom React hooks

#### Shared Layer
- ✅ `src/shared/config/` - Configuration management
- ✅ `src/shared/container/Container.ts` - Dependency injection
- ✅ `src/shared/utils/` - Common utilities

### Phase 3: Migration Tasks

#### 3.1 Update Import Statements

**Old imports:**
```typescript
import { tradingService } from '../../../lib/services/trading.service';
import { User } from '../../../lib/types';
```

**New imports:**
```typescript
import { container } from '../../../shared/container/Container';
import { User } from '../../../domain/user/entities/User';
```

#### 3.2 Replace Service Calls

**Old pattern:**
```typescript
// Direct service calls
const trade = await tradingService.createTrade(tradeData);
```

**New pattern:**
```typescript
// Use case execution
const trade = await container.createTradeUseCase.execute(tradeData);
```

#### 3.3 Update Component Structure

**Old structure:**
```
app/components/
├── Button.tsx
├── OrderForm.tsx
└── ZerodhaStatus.tsx
```

**New structure:**
```
src/presentation/components/
├── ui/                    # Reusable UI components
│   ├── button.tsx
│   └── input.tsx
├── features/              # Feature-specific components
│   ├── trading/
│   │   ├── TradeForm.tsx
│   │   └── TradeList.tsx
│   └── copy-trading/
│       └── RelationshipForm.tsx
└── layout/               # Layout components
    └── global-navbar.tsx
```

#### 3.4 Update API Routes

**Old pattern:**
```typescript
// app/api/trading/route.ts
import { tradingService } from '../../../lib/services/trading.service';

export async function POST(request: Request) {
  const result = await tradingService.createTrade(data);
  return Response.json(result);
}
```

**New pattern:**
```typescript
// src/app/api/trading/create/route.ts
import { container } from '../../../../shared/container/Container';

export async function POST(request: NextRequest) {
  const result = await container.createTradeUseCase.execute(data);
  return NextResponse.json(result);
}
```

### Phase 4: File Mapping

#### Legacy → New Structure Mapping

| Legacy Location | New Location | Status |
|----------------|--------------|---------|
| `lib/services/trading.service.ts` | `src/application/use-cases/trading/` | ✅ Migrated |
| `lib/types/index.ts` | `src/domain/*/entities/` + `src/shared/types/` | ✅ Migrated |
| `app/components/OrderForm.tsx` | `src/presentation/components/features/trading/TradeForm.tsx` | ✅ Created |
| `app/utils/zerodha.ts` | `src/infrastructure/brokers/zerodha/ZerodhaAdapter.ts` | ✅ Migrated |
| `lib/prisma.ts` | `src/shared/config/database.ts` | ✅ Migrated |

### Phase 5: Testing Strategy

#### 5.1 Domain Layer Tests
```typescript
// tests/domain/trading/entities/Trade.test.ts
describe('Trade Entity', () => {
  it('should create a valid trade', () => {
    const trade = Trade.create({
      id: TradeId.generate(),
      userId: UserId.generate(),
      symbol: Symbol.fromString('RELIANCE'),
      // ... other properties
    });
    
    expect(trade.isPending()).toBe(true);
  });
});
```

#### 5.2 Use Case Tests
```typescript
// tests/application/use-cases/CreateTradeUseCase.test.ts
describe('CreateTradeUseCase', () => {
  it('should create trade successfully', async () => {
    const mockTradeRepo = createMockTradeRepository();
    const useCase = new CreateTradeUseCase(mockTradeRepo, ...);
    
    const result = await useCase.execute(validTradeDTO);
    
    expect(result.id).toBeDefined();
  });
});
```

### Phase 6: Environment Setup

#### 6.1 Update Environment Variables
Ensure these variables are set:
```env
# Database
DATABASE_URL="your-database-url"
DIRECT_URL="your-direct-url"

# Authentication
JWT_SECRET="your-jwt-secret"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# Zerodha
NEXT_PUBLIC_ZERODHA_API_KEY="your-api-key"
ZERODHA_API_SECRET="your-api-secret"
```

#### 6.2 Update Package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "test": "jest",
    "test:domain": "jest tests/domain",
    "test:application": "jest tests/application",
    "lint": "eslint src/",
    "type-check": "tsc --noEmit"
  }
}
```

### Phase 7: Gradual Migration Strategy

#### 7.1 Parallel Implementation
1. Keep old structure running
2. Implement new features using new architecture
3. Gradually migrate existing features
4. Remove old code once migration is complete

#### 7.2 Feature Flags
Use feature flags to toggle between old and new implementations:
```typescript
const useNewArchitecture = process.env.USE_NEW_ARCHITECTURE === 'true';

if (useNewArchitecture) {
  return await container.createTradeUseCase.execute(data);
} else {
  return await legacyTradingService.createTrade(data);
}
```

### Phase 8: Validation Checklist

- [ ] All domain entities have proper business logic
- [ ] Use cases are properly tested
- [ ] Repository interfaces are implemented
- [ ] API routes use dependency injection
- [ ] Components use new hooks
- [ ] Environment variables are configured
- [ ] Tests are passing
- [ ] Documentation is updated

## Benefits After Migration

1. **Better Testability**: Domain logic can be tested in isolation
2. **Improved Maintainability**: Clear separation of concerns
3. **Enhanced Scalability**: Modular structure supports growth
4. **Business Alignment**: Code reflects business domains
5. **Technology Flexibility**: Easy to swap implementations

## Troubleshooting

### Common Issues

1. **Import Errors**: Update import paths to new structure
2. **Type Errors**: Use new domain entities instead of plain objects
3. **Dependency Issues**: Ensure proper dependency injection setup
4. **Test Failures**: Update test imports and mocking strategies

### Getting Help

1. Review the `ENTERPRISE_ARCHITECTURE.md` for detailed explanations
2. Check existing implementations in the new structure
3. Follow the patterns established in completed migrations
4. Ensure all dependencies are properly injected

This migration guide provides a structured approach to transitioning to the enterprise architecture while maintaining application functionality.
