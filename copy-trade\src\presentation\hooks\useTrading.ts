'use client';

import { useState, useCallback } from 'react';
import { CreateTradeDTO } from '../../application/dto/trading/CreateTradeDTO';
import { ExecuteTradeDTO } from '../../application/dto/trading/ExecuteTradeDTO';
import { TradeResponseDTO } from '../../application/dto/trading/TradeResponseDTO';

export function useTrading() {
  const [trades, setTrades] = useState<TradeResponseDTO[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createTrade = useCallback(async (tradeData: CreateTradeDTO): Promise<TradeResponseDTO> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/trading/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create trade');
      }

      const trade = await response.json();
      setTrades(prev => [trade, ...prev]);
      return trade;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const executeTrade = useCallback(async (executeData: ExecuteTradeDTO): Promise<TradeResponseDTO> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/trading/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(executeData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to execute trade');
      }

      const updatedTrade = await response.json();
      setTrades(prev => prev.map(trade => 
        trade.id === updatedTrade.id ? updatedTrade : trade
      ));
      return updatedTrade;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const cancelTrade = useCallback(async (tradeId: string, userId: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/trading/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tradeId, userId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to cancel trade');
      }

      const updatedTrade = await response.json();
      setTrades(prev => prev.map(trade => 
        trade.id === updatedTrade.id ? updatedTrade : trade
      ));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTrades = useCallback(async (userId: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/trading/list?userId=${userId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch trades');
      }

      const tradesData = await response.json();
      setTrades(tradesData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    trades,
    isLoading,
    error,
    createTrade,
    executeTrade,
    cancelTrade,
    fetchTrades,
    clearError,
  };
}
