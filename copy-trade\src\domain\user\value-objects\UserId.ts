import { v4 as uuidv4 } from 'uuid';

export class UserId {
  private constructor(private readonly _value: string) {
    if (!_value || _value.trim().length === 0) {
      throw new Error('UserId cannot be empty');
    }
  }

  public static generate(): UserId {
    return new UserId(uuidv4());
  }

  public static fromString(value: string): UserId {
    return new UserId(value);
  }

  public get value(): string {
    return this._value;
  }

  public equals(other: UserId): boolean {
    return this._value === other._value;
  }

  public toString(): string {
    return this._value;
  }
}
