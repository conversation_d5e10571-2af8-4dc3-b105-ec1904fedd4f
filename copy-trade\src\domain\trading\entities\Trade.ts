import { TradeStatus, OrderType, TransactionType, ProductType } from '../enums/TradeEnums';
import { TradeId } from '../value-objects/TradeId';
import { UserId } from '../../user/value-objects/UserId';
import { Symbol } from '../value-objects/Symbol';
import { Price } from '../value-objects/Price';
import { Quantity } from '../value-objects/Quantity';

export class Trade {
  private constructor(
    private readonly _id: TradeId,
    private readonly _userId: UserId,
    private readonly _symbol: Symbol,
    private readonly _exchange: string,
    private readonly _transactionType: TransactionType,
    private readonly _quantity: Quantity,
    private readonly _price: Price,
    private readonly _orderType: OrderType,
    private readonly _productType: ProductType,
    private _status: TradeStatus,
    private readonly _zerodhaOrderId?: string,
    private _executedAt?: Date,
    private readonly _isDemo: boolean = false,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date()
  ) {}

  public static create(params: {
    id: TradeId;
    userId: UserId;
    symbol: Symbol;
    exchange: string;
    transactionType: TransactionType;
    quantity: Quantity;
    price: Price;
    orderType: OrderType;
    productType: ProductType;
    zerodhaOrderId?: string;
    isDemo?: boolean;
  }): Trade {
    return new Trade(
      params.id,
      params.userId,
      params.symbol,
      params.exchange,
      params.transactionType,
      params.quantity,
      params.price,
      params.orderType,
      params.productType,
      TradeStatus.PENDING,
      params.zerodhaOrderId,
      undefined,
      params.isDemo || false
    );
  }

  public static fromPersistence(data: {
    id: string;
    userId: string;
    symbol: string;
    exchange: string;
    transactionType: string;
    quantity: number;
    price: number;
    orderType: string;
    productType: string;
    status: string;
    zerodhaOrderId?: string;
    executedAt?: Date;
    isDemo: boolean;
    createdAt: Date;
    updatedAt: Date;
  }): Trade {
    return new Trade(
      TradeId.fromString(data.id),
      UserId.fromString(data.userId),
      Symbol.fromString(data.symbol),
      data.exchange,
      data.transactionType as TransactionType,
      Quantity.fromNumber(data.quantity),
      Price.fromNumber(data.price),
      data.orderType as OrderType,
      data.productType as ProductType,
      data.status as TradeStatus,
      data.zerodhaOrderId,
      data.executedAt,
      data.isDemo,
      data.createdAt,
      data.updatedAt
    );
  }

  // Getters
  public get id(): TradeId { return this._id; }
  public get userId(): UserId { return this._userId; }
  public get symbol(): Symbol { return this._symbol; }
  public get exchange(): string { return this._exchange; }
  public get transactionType(): TransactionType { return this._transactionType; }
  public get quantity(): Quantity { return this._quantity; }
  public get price(): Price { return this._price; }
  public get orderType(): OrderType { return this._orderType; }
  public get productType(): ProductType { return this._productType; }
  public get status(): TradeStatus { return this._status; }
  public get zerodhaOrderId(): string | undefined { return this._zerodhaOrderId; }
  public get executedAt(): Date | undefined { return this._executedAt; }
  public get isDemo(): boolean { return this._isDemo; }
  public get createdAt(): Date { return this._createdAt; }
  public get updatedAt(): Date { return this._updatedAt; }

  // Business methods
  public execute(): void {
    if (this._status !== TradeStatus.PENDING) {
      throw new Error('Trade can only be executed when in PENDING status');
    }
    this._status = TradeStatus.COMPLETE;
    this._executedAt = new Date();
    this._updatedAt = new Date();
  }

  public cancel(): void {
    if (this._status === TradeStatus.COMPLETE) {
      throw new Error('Cannot cancel a completed trade');
    }
    this._status = TradeStatus.CANCELLED;
    this._updatedAt = new Date();
  }

  public reject(): void {
    if (this._status === TradeStatus.COMPLETE) {
      throw new Error('Cannot reject a completed trade');
    }
    this._status = TradeStatus.REJECTED;
    this._updatedAt = new Date();
  }

  public isPending(): boolean {
    return this._status === TradeStatus.PENDING;
  }

  public isCompleted(): boolean {
    return this._status === TradeStatus.COMPLETE;
  }

  public isCancelled(): boolean {
    return this._status === TradeStatus.CANCELLED;
  }

  public isRejected(): boolean {
    return this._status === TradeStatus.REJECTED;
  }

  public toPersistence(): any {
    return {
      id: this._id.value,
      userId: this._userId.value,
      symbol: this._symbol.value,
      exchange: this._exchange,
      transactionType: this._transactionType,
      quantity: this._quantity.value,
      price: this._price.value,
      orderType: this._orderType,
      productType: this._productType,
      status: this._status,
      zerodhaOrderId: this._zerodhaOrderId,
      executedAt: this._executedAt,
      isDemo: this._isDemo,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    };
  }
}
