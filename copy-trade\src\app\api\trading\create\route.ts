import { NextRequest, NextResponse } from 'next/server';
import { container } from '../../../../shared/container/Container';
import { logger } from '../../../../shared/utils/logger';
import { Validator } from '../../../../shared/utils/validation';
import { CreateTradeDTO } from '../../../../application/dto/trading/CreateTradeDTO';

export async function POST(request: NextRequest) {
  try {
    const body: CreateTradeDTO = await request.json();

    // Validate input
    Validator.validateRequired(body.userId, 'userId');
    Validator.validateUUID(body.userId, 'userId');
    Validator.validateSymbol(body.symbol);
    Validator.validateTradeQuantity(body.quantity);
    Validator.validateTradePrice(body.price);
    Validator.validateRequired(body.exchange, 'exchange');
    Validator.validateRequired(body.transactionType, 'transactionType');
    Validator.validateRequired(body.orderType, 'orderType');
    Validator.validateRequired(body.productType, 'productType');

    // Execute use case
    const result = await container.createTradeUseCase.execute(body);

    logger.info('Trade created successfully', {
      tradeId: result.id,
      userId: body.userId,
      symbol: body.symbol
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    logger.error('Failed to create trade', error instanceof Error ? error : new Error('Unknown error'));

    if (error instanceof Error) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
