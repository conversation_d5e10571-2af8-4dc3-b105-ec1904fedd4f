{"version": 3, "names": ["circleSet", "Set", "depth", "deepClone", "value", "cache", "allowCircle", "has", "get", "clear", "Error", "add", "cloned", "Array", "isArray", "length", "set", "i", "keys", "Object", "key", "delete", "_default", "Map", "_", "structuredClone"], "sources": ["../../../src/transformation/util/clone-deep.ts"], "sourcesContent": ["const circleSet = new Set();\nlet depth = 0;\n// https://github.com/babel/babel/pull/14583#discussion_r882828856\nfunction deepClone(\n  value: any,\n  cache: Map<any, any>,\n  allowCircle: boolean,\n): any {\n  if (value !== null) {\n    if (allowCircle) {\n      if (cache.has(value)) return cache.get(value);\n    } else if (++depth > 250) {\n      if (circleSet.has(value)) {\n        depth = 0;\n        circleSet.clear();\n        throw new Error(\"Babel-deepClone: Cycles are not allowed in AST\");\n      }\n      circleSet.add(value);\n    }\n    let cloned: any;\n    if (Array.isArray(value)) {\n      cloned = new Array(value.length);\n      if (allowCircle) cache.set(value, cloned);\n      for (let i = 0; i < value.length; i++) {\n        cloned[i] =\n          typeof value[i] !== \"object\"\n            ? value[i]\n            : deepClone(value[i], cache, allowCircle);\n      }\n    } else {\n      cloned = {};\n      if (allowCircle) cache.set(value, cloned);\n      const keys = Object.keys(value);\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        cloned[key] =\n          typeof value[key] !== \"object\"\n            ? value[key]\n            : deepClone(\n                value[key],\n                cache,\n                allowCircle ||\n                  key === \"leadingComments\" ||\n                  key === \"innerComments\" ||\n                  key === \"trailingComments\" ||\n                  key === \"extra\",\n              );\n      }\n    }\n    if (!allowCircle) {\n      if (depth-- > 250) circleSet.delete(value);\n    }\n    return cloned;\n  }\n  return value;\n}\n\nexport default function <T>(value: T): T {\n  if (typeof value !== \"object\") return value;\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (!process.env.IS_PUBLISH && depth > 0) {\n      throw new Error(\"depth > 0\");\n    }\n    return deepClone(value, new Map(), false);\n  } else {\n    try {\n      return deepClone(value, new Map(), true);\n    } catch (_) {\n      return structuredClone(value);\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,IAAIC,KAAK,GAAG,CAAC;AAEb,SAASC,SAASA,CAChBC,KAAU,EACVC,KAAoB,EACpBC,WAAoB,EACf;EACL,IAAIF,KAAK,KAAK,IAAI,EAAE;IAClB,IAAIE,WAAW,EAAE;MACf,IAAID,KAAK,CAACE,GAAG,CAACH,KAAK,CAAC,EAAE,OAAOC,KAAK,CAACG,GAAG,CAACJ,KAAK,CAAC;IAC/C,CAAC,MAAM,IAAI,EAAEF,KAAK,GAAG,GAAG,EAAE;MACxB,IAAIF,SAAS,CAACO,GAAG,CAACH,KAAK,CAAC,EAAE;QACxBF,KAAK,GAAG,CAAC;QACTF,SAAS,CAACS,KAAK,CAAC,CAAC;QACjB,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;MACnE;MACAV,SAAS,CAACW,GAAG,CAACP,KAAK,CAAC;IACtB;IACA,IAAIQ,MAAW;IACf,IAAIC,KAAK,CAACC,OAAO,CAACV,KAAK,CAAC,EAAE;MACxBQ,MAAM,GAAG,IAAIC,KAAK,CAACT,KAAK,CAACW,MAAM,CAAC;MAChC,IAAIT,WAAW,EAAED,KAAK,CAACW,GAAG,CAACZ,KAAK,EAAEQ,MAAM,CAAC;MACzC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,KAAK,CAACW,MAAM,EAAEE,CAAC,EAAE,EAAE;QACrCL,MAAM,CAACK,CAAC,CAAC,GACP,OAAOb,KAAK,CAACa,CAAC,CAAC,KAAK,QAAQ,GACxBb,KAAK,CAACa,CAAC,CAAC,GACRd,SAAS,CAACC,KAAK,CAACa,CAAC,CAAC,EAAEZ,KAAK,EAAEC,WAAW,CAAC;MAC/C;IACF,CAAC,MAAM;MACLM,MAAM,GAAG,CAAC,CAAC;MACX,IAAIN,WAAW,EAAED,KAAK,CAACW,GAAG,CAACZ,KAAK,EAAEQ,MAAM,CAAC;MACzC,MAAMM,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACd,KAAK,CAAC;MAC/B,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACH,MAAM,EAAEE,CAAC,EAAE,EAAE;QACpC,MAAMG,GAAG,GAAGF,IAAI,CAACD,CAAC,CAAC;QACnBL,MAAM,CAACQ,GAAG,CAAC,GACT,OAAOhB,KAAK,CAACgB,GAAG,CAAC,KAAK,QAAQ,GAC1BhB,KAAK,CAACgB,GAAG,CAAC,GACVjB,SAAS,CACPC,KAAK,CAACgB,GAAG,CAAC,EACVf,KAAK,EACLC,WAAW,IACTc,GAAG,KAAK,iBAAiB,IACzBA,GAAG,KAAK,eAAe,IACvBA,GAAG,KAAK,kBAAkB,IAC1BA,GAAG,KAAK,OACZ,CAAC;MACT;IACF;IACA,IAAI,CAACd,WAAW,EAAE;MAChB,IAAIJ,KAAK,EAAE,GAAG,GAAG,EAAEF,SAAS,CAACqB,MAAM,CAACjB,KAAK,CAAC;IAC5C;IACA,OAAOQ,MAAM;EACf;EACA,OAAOR,KAAK;AACd;AAEe,SAAAkB,SAAalB,KAAQ,EAAK;EACvC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAOpC;IACL,IAAI;MACF,OAAOD,SAAS,CAACC,KAAK,EAAE,IAAImB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;IAC1C,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAOC,eAAe,CAACrB,KAAK,CAAC;IAC/B;EACF;AACF;AAAC", "ignoreList": []}