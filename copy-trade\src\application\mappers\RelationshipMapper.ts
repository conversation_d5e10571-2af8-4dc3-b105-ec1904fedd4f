import { MasterChildRelationship } from '../../domain/copy-trading/entities/MasterChildRelationship';
import { RelationshipResponseDTO } from '../dto/copy-trading/RelationshipResponseDTO';

export class RelationshipMapper {
  public static toResponseDTO(relationship: MasterChildRelationship): RelationshipResponseDTO {
    return {
      id: relationship.id.value,
      masterId: relationship.masterId.value,
      childId: relationship.childId.value,
      isActive: relationship.isActive,
      createdAt: relationship.createdAt,
      updatedAt: relationship.updatedAt
    };
  }

  public static toResponseDTOList(relationships: MasterChildRelationship[]): RelationshipResponseDTO[] {
    return relationships.map(relationship => this.toResponseDTO(relationship));
  }
}
