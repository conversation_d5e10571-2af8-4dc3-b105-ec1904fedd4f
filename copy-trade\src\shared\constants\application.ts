// Application metadata
export const APP_CONFIG = {
  name: 'CopyTrade',
  description: 'Enterprise Copy Trading Platform',
  version: '2.0.0',
  author: 'CopyTrade Team',
} as const;

// Trading constants
export const TRADING_CONSTANTS = {
  EXCHANGES: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'] as const,
  ORDER_TYPES: ['MARKET', 'LIMIT', 'SL', 'SL_M'] as const,
  PRODUCT_TYPES: ['CNC', 'MIS', 'NRML'] as const,
  TRANSACTION_TYPES: ['BUY', 'SELL'] as const,
  TRADE_STATUSES: ['PENDING', 'COMPLETE', 'CANCELLED', 'REJECTED'] as const,
} as const;

// User constants
export const USER_CONSTANTS = {
  ROLES: ['MASTER', 'CHILD'] as const,
  STATUSES: ['ACTIVE', 'INACTIVE', 'SUSPENDED'] as const,
} as const;

// Copy trading constants
export const COPY_TRADING_CONSTANTS = {
  COPY_STATUSES: ['PENDING', 'COMPLETED', 'FAILED'] as const,
  RELATIONSHIP_STATUSES: ['ACTIVE', 'INACTIVE', 'DELETED'] as const,
  MAX_CHILDREN_PER_MASTER: 100,
  MAX_MASTERS_PER_CHILD: 10,
} as const;

// Demo mode constants
export const DEMO_CONFIG = {
  DEFAULT_DELAY: 1000,
  MAX_DELAY: 3000,
  LOG_PREFIX: '[DEMO]',
  MOCK_ORDER_ID_PREFIX: 'DEMO_',
} as const;

// API constants
export const API_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// Validation constants
export const VALIDATION_CONSTANTS = {
  MIN_TRADE_QUANTITY: 1,
  MAX_TRADE_QUANTITY: 10000,
  MIN_TRADE_PRICE: 0.01,
  MAX_TRADE_PRICE: 100000,
  PASSWORD_MIN_LENGTH: 8,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  SYMBOL_REGEX: /^[A-Z0-9_-]+$/,
} as const;
