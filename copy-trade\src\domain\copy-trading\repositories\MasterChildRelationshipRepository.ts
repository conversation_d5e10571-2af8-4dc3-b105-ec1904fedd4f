import { MasterChildRelationship } from '../entities/MasterChildRelationship';
import { RelationshipId } from '../value-objects/RelationshipId';
import { UserId } from '../../user/value-objects/UserId';

export interface MasterChildRelationshipRepository {
  save(relationship: MasterChildRelationship): Promise<void>;
  findById(id: RelationshipId): Promise<MasterChildRelationship | null>;
  findByMasterId(masterId: UserId): Promise<MasterChildRelationship[]>;
  findByChildId(childId: UserId): Promise<MasterChildRelationship[]>;
  findActiveByMasterId(masterId: UserId): Promise<MasterChildRelationship[]>;
  findActiveByChildId(childId: UserId): Promise<MasterChildRelationship[]>;
  existsActiveRelationship(masterId: UserId, childId: UserId): Promise<boolean>;
  update(relationship: MasterChildRelationship): Promise<void>;
  delete(id: RelationshipId): Promise<void>;
}
