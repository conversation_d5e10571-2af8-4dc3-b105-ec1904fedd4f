import { Trade } from '../entities/Trade';
import { TradeRepository } from '../repositories/TradeRepository';
import { UserId } from '../../user/value-objects/UserId';
import { TradeStatus } from '../enums/TradeEnums';

export class TradingDomainService {
  constructor(private readonly tradeRepository: TradeRepository) {}

  public async canExecuteTrade(trade: Trade): Promise<boolean> {
    // Business rule: Can only execute pending trades
    if (!trade.isPending()) {
      return false;
    }

    // Business rule: Demo trades can always be executed
    if (trade.isDemo) {
      return true;
    }

    // Add more business rules here as needed
    return true;
  }

  public async validateTradeForUser(trade: Trade, userId: UserId): Promise<boolean> {
    // Business rule: User can only trade their own trades
    return trade.userId.equals(userId);
  }

  public async calculateTotalTradeValue(trades: Trade[]): Promise<number> {
    return trades.reduce((total, trade) => {
      if (trade.isCompleted()) {
        return total + (trade.price.value * trade.quantity.value);
      }
      return total;
    }, 0);
  }

  public async findActiveTradesForUser(userId: UserId): Promise<Trade[]> {
    const allTrades = await this.tradeRepository.findByUserId(userId);
    return allTrades.filter(trade => 
      trade.status === TradeStatus.PENDING || trade.status === TradeStatus.COMPLETE
    );
  }
}
