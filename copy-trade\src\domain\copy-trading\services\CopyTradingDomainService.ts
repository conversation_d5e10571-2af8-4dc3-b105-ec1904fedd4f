import { MasterChildRelationship } from '../entities/MasterChildRelationship';
import { TradeCopy } from '../entities/TradeCopy';
import { Trade } from '../../trading/entities/Trade';
import { UserId } from '../../user/value-objects/UserId';
import { MasterChildRelationshipRepository } from '../repositories/MasterChildRelationshipRepository';
import { TradeCopyRepository } from '../repositories/TradeCopyRepository';

export class CopyTradingDomainService {
  constructor(
    private readonly relationshipRepository: MasterChildRelationshipRepository,
    private readonly tradeCopyRepository: TradeCopyRepository
  ) {}

  public async canCopyTrade(masterId: UserId, childId: UserId): Promise<boolean> {
    // Business rule: Must have active relationship
    const hasActiveRelationship = await this.relationshipRepository.existsActiveRelationship(masterId, childId);
    return hasActiveRelationship;
  }

  public async getChildrenForMaster(masterId: UserId): Promise<MasterChildRelationship[]> {
    return await this.relationshipRepository.findActiveByMasterId(masterId);
  }

  public async getMastersForChild(childId: UserId): Promise<MasterChildRelationship[]> {
    return await this.relationshipRepository.findActiveByChildId(childId);
  }

  public async validateRelationshipCreation(masterId: UserId, childId: UserId): Promise<void> {
    // Business rule: Cannot create relationship with self
    if (masterId.equals(childId)) {
      throw new Error('Cannot create relationship with self');
    }

    // Business rule: Cannot have duplicate active relationships
    const existingRelationship = await this.relationshipRepository.existsActiveRelationship(masterId, childId);
    if (existingRelationship) {
      throw new Error('Active relationship already exists');
    }
  }

  public async calculateCopySuccessRate(masterId: UserId): Promise<number> {
    const allCopies = await this.tradeCopyRepository.findByMasterId(masterId);
    if (allCopies.length === 0) {
      return 0;
    }

    const successfulCopies = allCopies.filter(copy => copy.isCompleted());
    return (successfulCopies.length / allCopies.length) * 100;
  }

  public async getTotalCopiesForMaster(masterId: UserId): Promise<number> {
    const copies = await this.tradeCopyRepository.findByMasterId(masterId);
    return copies.length;
  }
}
