export class Symbol {
  private constructor(private readonly _value: string) {
    if (!_value || _value.trim().length === 0) {
      throw new Error('Symbol cannot be empty');
    }
    if (!/^[A-Z0-9_-]+$/.test(_value)) {
      throw new Error('Symbol must contain only uppercase letters, numbers, underscores, and hyphens');
    }
  }

  public static fromString(value: string): Symbol {
    return new Symbol(value.toUpperCase());
  }

  public get value(): string {
    return this._value;
  }

  public equals(other: Symbol): boolean {
    return this._value === other._value;
  }

  public toString(): string {
    return this._value;
  }
}
