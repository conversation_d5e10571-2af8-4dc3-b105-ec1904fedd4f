// Common domain types
export interface DomainEvent {
  eventId: string;
  occurredOn: Date;
  eventType: string;
}

export interface AggregateRoot {
  id: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface Repository<T, ID> {
  save(entity: T): Promise<void>;
  findById(id: ID): Promise<T | null>;
  update(entity: T): Promise<void>;
  delete(id: ID): Promise<void>;
}

export interface ValueObject<T> {
  equals(other: T): boolean;
}

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Filter types
export interface DateRange {
  from: Date;
  to: Date;
}

export interface FilterParams {
  dateRange?: DateRange;
  status?: string[];
  search?: string;
}
