'use client';

import { useState } from 'react';
import { But<PERSON> } from '../../ui/button';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { CreateRelationshipDTO } from '../../../application/dto/copy-trading/CreateRelationshipDTO';

interface RelationshipFormProps {
  onSubmit: (relationship: CreateRelationshipDTO) => Promise<void>;
  isLoading?: boolean;
  currentUserId: string;
  userRole: 'MASTER' | 'CHILD';
}

export function RelationshipForm({ onSubmit, isLoading = false, currentUserId, userRole }: RelationshipFormProps) {
  const [formData, setFormData] = useState({
    email: '',
    name: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // For now, we'll use email as a placeholder for user ID
    // In a real implementation, you'd look up the user by email first
    const relationshipData: CreateRelationshipDTO = userRole === 'MASTER' 
      ? { masterId: currentUserId, childId: formData.email } // This should be actual user ID
      : { masterId: formData.email, childId: currentUserId }; // This should be actual user ID
    
    await onSubmit(relationshipData);
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>
          {userRole === 'MASTER' ? 'Add Child User' : 'Connect to Master'}
        </CardTitle>
        <CardDescription>
          {userRole === 'MASTER' 
            ? 'Add a child user to copy your trades'
            : 'Connect to a master user to copy their trades'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">
              {userRole === 'MASTER' ? 'Child User Email' : 'Master User Email'}
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">
              {userRole === 'MASTER' ? 'Child User Name' : 'Master User Name'}
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Full Name"
              required
            />
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading 
              ? 'Creating Relationship...' 
              : userRole === 'MASTER' 
                ? 'Add Child User' 
                : 'Connect to Master'
            }
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
