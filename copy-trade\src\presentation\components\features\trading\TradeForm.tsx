'use client';

import { useState } from 'react';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { CreateTradeDTO } from '../../../application/dto/trading/CreateTradeDTO';

interface TradeFormProps {
  onSubmit: (trade: CreateTradeDTO) => Promise<void>;
  isLoading?: boolean;
  userId: string;
}

export function TradeForm({ onSubmit, isLoading = false, userId }: TradeFormProps) {
  const [formData, setFormData] = useState<Omit<CreateTradeDTO, 'userId'>>({
    symbol: '',
    exchange: 'NSE',
    transactionType: 'BUY',
    quantity: 1,
    price: 0,
    orderType: 'MARKET',
    productType: 'CNC',
    isDemo: false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit({ ...formData, userId });
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Place Trade</CardTitle>
        <CardDescription>
          Enter trade details to place an order
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="symbol">Symbol</Label>
            <Input
              id="symbol"
              value={formData.symbol}
              onChange={(e) => handleInputChange('symbol', e.target.value)}
              placeholder="e.g., RELIANCE"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="exchange">Exchange</Label>
            <select
              id="exchange"
              value={formData.exchange}
              onChange={(e) => handleInputChange('exchange', e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="NSE">NSE</option>
              <option value="BSE">BSE</option>
              <option value="NFO">NFO</option>
              <option value="BFO">BFO</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="transactionType">Transaction Type</Label>
            <select
              id="transactionType"
              value={formData.transactionType}
              onChange={(e) => handleInputChange('transactionType', e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="BUY">BUY</option>
              <option value="SELL">SELL</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', parseInt(e.target.value))}
              min="1"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="orderType">Order Type</Label>
            <select
              id="orderType"
              value={formData.orderType}
              onChange={(e) => handleInputChange('orderType', e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="MARKET">MARKET</option>
              <option value="LIMIT">LIMIT</option>
              <option value="SL">SL</option>
              <option value="SL_M">SL-M</option>
            </select>
          </div>

          {formData.orderType === 'LIMIT' && (
            <div className="space-y-2">
              <Label htmlFor="price">Price</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => handleInputChange('price', parseFloat(e.target.value))}
                min="0"
                required
              />
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="productType">Product Type</Label>
            <select
              id="productType"
              value={formData.productType}
              onChange={(e) => handleInputChange('productType', e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="CNC">CNC</option>
              <option value="MIS">MIS</option>
              <option value="NRML">NRML</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isDemo"
              checked={formData.isDemo}
              onChange={(e) => handleInputChange('isDemo', e.target.checked)}
            />
            <Label htmlFor="isDemo">Demo Mode</Label>
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? 'Placing Trade...' : 'Place Trade'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
