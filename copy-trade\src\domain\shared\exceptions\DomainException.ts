export abstract class DomainException extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly details?: any
  ) {
    super(message);
    this.name = this.constructor.name;
  }
}

export class ValidationException extends DomainException {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details);
  }
}

export class BusinessRuleViolationException extends DomainException {
  constructor(message: string, details?: any) {
    super(message, 'BUSINESS_RULE_VIOLATION', details);
  }
}

export class EntityNotFoundException extends DomainException {
  constructor(entityName: string, id: string) {
    super(`${entityName} with ID ${id} not found`, 'ENTITY_NOT_FOUND', { entityName, id });
  }
}
