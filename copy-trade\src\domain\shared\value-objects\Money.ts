export class Money {
  private constructor(
    private readonly _amount: number,
    private readonly _currency: string = 'INR'
  ) {
    if (_amount < 0) {
      throw new Error('Money amount cannot be negative');
    }
    if (!Number.isFinite(_amount)) {
      throw new Error('Money amount must be a finite number');
    }
    if (!_currency || _currency.trim().length === 0) {
      throw new Error('Currency cannot be empty');
    }
  }

  public static fromAmount(amount: number, currency: string = 'INR'): Money {
    return new Money(amount, currency.toUpperCase());
  }

  public get amount(): number {
    return this._amount;
  }

  public get currency(): string {
    return this._currency;
  }

  public equals(other: Money): boolean {
    return this._amount === other._amount && this._currency === other._currency;
  }

  public add(other: Money): Money {
    if (this._currency !== other._currency) {
      throw new Error('Cannot add money with different currencies');
    }
    return new Money(this._amount + other._amount, this._currency);
  }

  public subtract(other: Money): Money {
    if (this._currency !== other._currency) {
      throw new Error('Cannot subtract money with different currencies');
    }
    return new Money(this._amount - other._amount, this._currency);
  }

  public multiply(factor: number): Money {
    return new Money(this._amount * factor, this._currency);
  }

  public isGreaterThan(other: Money): boolean {
    if (this._currency !== other._currency) {
      throw new Error('Cannot compare money with different currencies');
    }
    return this._amount > other._amount;
  }

  public isLessThan(other: Money): boolean {
    if (this._currency !== other._currency) {
      throw new Error('Cannot compare money with different currencies');
    }
    return this._amount < other._amount;
  }

  public toString(): string {
    return `${this._currency} ${this._amount.toFixed(2)}`;
  }
}
