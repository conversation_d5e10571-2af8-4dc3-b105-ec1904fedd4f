import { MasterChildRelationship } from '../../../domain/copy-trading/entities/MasterChildRelationship';
import { MasterChildRelationshipRepository } from '../../../domain/copy-trading/repositories/MasterChildRelationshipRepository';
import { UserRepository } from '../../../domain/user/repositories/UserRepository';
import { CopyTradingDomainService } from '../../../domain/copy-trading/services/CopyTradingDomainService';
import { RelationshipId } from '../../../domain/copy-trading/value-objects/RelationshipId';
import { UserId } from '../../../domain/user/value-objects/UserId';
import { UserRole } from '../../../domain/user/enums/UserEnums';
import { CreateRelationshipDTO } from '../../dto/copy-trading/CreateRelationshipDTO';
import { RelationshipResponseDTO } from '../../dto/copy-trading/RelationshipResponseDTO';
import { RelationshipMapper } from '../../mappers/RelationshipMapper';

export class CreateRelationshipUseCase {
  constructor(
    private readonly relationshipRepository: MasterChildRelationshipRepository,
    private readonly userRepository: UserRepository,
    private readonly copyTradingDomainService: CopyTradingDomainService
  ) {}

  public async execute(dto: CreateRelationshipDTO): Promise<RelationshipResponseDTO> {
    // Validate users exist
    const masterId = UserId.fromString(dto.masterId);
    const childId = UserId.fromString(dto.childId);

    const master = await this.userRepository.findById(masterId);
    if (!master) {
      throw new Error('Master user not found');
    }

    const child = await this.userRepository.findById(childId);
    if (!child) {
      throw new Error('Child user not found');
    }

    // Validate user roles
    if (master.role !== UserRole.MASTER) {
      throw new Error('Master user must have MASTER role');
    }

    if (child.role !== UserRole.CHILD) {
      throw new Error('Child user must have CHILD role');
    }

    // Validate relationship creation
    await this.copyTradingDomainService.validateRelationshipCreation(masterId, childId);

    // Create relationship
    const relationship = MasterChildRelationship.create({
      id: RelationshipId.generate(),
      masterId: masterId,
      childId: childId
    });

    // Save relationship
    await this.relationshipRepository.save(relationship);

    // Return response
    return RelationshipMapper.toResponseDTO(relationship);
  }
}
