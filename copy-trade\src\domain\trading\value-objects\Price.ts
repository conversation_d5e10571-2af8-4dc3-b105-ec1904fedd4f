export class Price {
  private constructor(private readonly _value: number) {
    if (_value < 0) {
      throw new Error('Price cannot be negative');
    }
    if (!Number.isFinite(_value)) {
      throw new Error('Price must be a finite number');
    }
  }

  public static fromNumber(value: number): Price {
    return new Price(value);
  }

  public get value(): number {
    return this._value;
  }

  public equals(other: Price): boolean {
    return this._value === other._value;
  }

  public isGreaterThan(other: Price): boolean {
    return this._value > other._value;
  }

  public isLessThan(other: Price): boolean {
    return this._value < other._value;
  }

  public add(other: Price): Price {
    return new Price(this._value + other._value);
  }

  public subtract(other: Price): Price {
    return new Price(this._value - other._value);
  }

  public multiply(factor: number): Price {
    return new Price(this._value * factor);
  }

  public toString(): string {
    return this._value.toFixed(2);
  }
}
