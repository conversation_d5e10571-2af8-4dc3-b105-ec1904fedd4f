import { TradeRepository } from '../../../domain/trading/repositories/TradeRepository';
import { TradingDomainService } from '../../../domain/trading/services/TradingDomainService';
import { TradeId } from '../../../domain/trading/value-objects/TradeId';
import { UserId } from '../../../domain/user/value-objects/UserId';
import { ExecuteTradeDTO } from '../../dto/trading/ExecuteTradeDTO';
import { TradeResponseDTO } from '../../dto/trading/TradeResponseDTO';
import { TradeMapper } from '../../mappers/TradeMapper';

export class ExecuteTradeUseCase {
  constructor(
    private readonly tradeRepository: TradeRepository,
    private readonly tradingDomainService: TradingDomainService
  ) {}

  public async execute(dto: ExecuteTradeDTO): Promise<TradeResponseDTO> {
    // Find trade
    const tradeId = TradeId.fromString(dto.tradeId);
    const trade = await this.tradeRepository.findById(tradeId);
    if (!trade) {
      throw new Error('Trade not found');
    }

    // Validate user can execute this trade
    const userId = UserId.fromString(dto.userId);
    const canExecute = await this.tradingDomainService.validateTradeForUser(trade, userId);
    if (!canExecute) {
      throw new Error('User is not authorized to execute this trade');
    }

    // Execute trade
    trade.execute();

    // Update trade
    await this.tradeRepository.update(trade);

    // Return response
    return TradeMapper.toResponseDTO(trade);
  }
}
