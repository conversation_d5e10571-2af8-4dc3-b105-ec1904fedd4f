import { PrismaClient } from '@prisma/client';
import { MasterChildRelationship } from '../../../domain/copy-trading/entities/MasterChildRelationship';
import { MasterChildRelationshipRepository } from '../../../domain/copy-trading/repositories/MasterChildRelationshipRepository';
import { RelationshipId } from '../../../domain/copy-trading/value-objects/RelationshipId';
import { UserId } from '../../../domain/user/value-objects/UserId';

export class PrismaMasterChildRelationshipRepository implements MasterChildRelationshipRepository {
  constructor(private readonly prisma: PrismaClient) {}

  public async save(relationship: MasterChildRelationship): Promise<void> {
    const data = relationship.toPersistence();
    await this.prisma.masterChildRelationship.create({
      data: {
        id: data.id,
        masterId: data.masterId,
        childId: data.childId,
        isActive: data.isActive,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: data.deletedAt
      }
    });
  }

  public async findById(id: RelationshipId): Promise<MasterChildRelationship | null> {
    const relationship = await this.prisma.masterChildRelationship.findUnique({
      where: { id: id.value }
    });

    if (!relationship) {
      return null;
    }

    return MasterChildRelationship.fromPersistence({
      id: relationship.id,
      masterId: relationship.masterId,
      childId: relationship.childId,
      isActive: relationship.isActive,
      createdAt: relationship.createdAt,
      updatedAt: relationship.updatedAt,
      deletedAt: relationship.deletedAt || undefined
    });
  }

  public async findByMasterId(masterId: UserId): Promise<MasterChildRelationship[]> {
    const relationships = await this.prisma.masterChildRelationship.findMany({
      where: { masterId: masterId.value },
      orderBy: { createdAt: 'desc' }
    });

    return relationships.map(relationship => MasterChildRelationship.fromPersistence({
      id: relationship.id,
      masterId: relationship.masterId,
      childId: relationship.childId,
      isActive: relationship.isActive,
      createdAt: relationship.createdAt,
      updatedAt: relationship.updatedAt,
      deletedAt: relationship.deletedAt || undefined
    }));
  }

  public async findByChildId(childId: UserId): Promise<MasterChildRelationship[]> {
    const relationships = await this.prisma.masterChildRelationship.findMany({
      where: { childId: childId.value },
      orderBy: { createdAt: 'desc' }
    });

    return relationships.map(relationship => MasterChildRelationship.fromPersistence({
      id: relationship.id,
      masterId: relationship.masterId,
      childId: relationship.childId,
      isActive: relationship.isActive,
      createdAt: relationship.createdAt,
      updatedAt: relationship.updatedAt,
      deletedAt: relationship.deletedAt || undefined
    }));
  }

  public async findActiveByMasterId(masterId: UserId): Promise<MasterChildRelationship[]> {
    const relationships = await this.prisma.masterChildRelationship.findMany({
      where: { 
        masterId: masterId.value,
        isActive: true,
        deletedAt: null
      },
      orderBy: { createdAt: 'desc' }
    });

    return relationships.map(relationship => MasterChildRelationship.fromPersistence({
      id: relationship.id,
      masterId: relationship.masterId,
      childId: relationship.childId,
      isActive: relationship.isActive,
      createdAt: relationship.createdAt,
      updatedAt: relationship.updatedAt,
      deletedAt: relationship.deletedAt || undefined
    }));
  }

  public async findActiveByChildId(childId: UserId): Promise<MasterChildRelationship[]> {
    const relationships = await this.prisma.masterChildRelationship.findMany({
      where: { 
        childId: childId.value,
        isActive: true,
        deletedAt: null
      },
      orderBy: { createdAt: 'desc' }
    });

    return relationships.map(relationship => MasterChildRelationship.fromPersistence({
      id: relationship.id,
      masterId: relationship.masterId,
      childId: relationship.childId,
      isActive: relationship.isActive,
      createdAt: relationship.createdAt,
      updatedAt: relationship.updatedAt,
      deletedAt: relationship.deletedAt || undefined
    }));
  }

  public async existsActiveRelationship(masterId: UserId, childId: UserId): Promise<boolean> {
    const count = await this.prisma.masterChildRelationship.count({
      where: {
        masterId: masterId.value,
        childId: childId.value,
        isActive: true,
        deletedAt: null
      }
    });
    return count > 0;
  }

  public async update(relationship: MasterChildRelationship): Promise<void> {
    const data = relationship.toPersistence();
    await this.prisma.masterChildRelationship.update({
      where: { id: data.id },
      data: {
        isActive: data.isActive,
        updatedAt: data.updatedAt,
        deletedAt: data.deletedAt
      }
    });
  }

  public async delete(id: RelationshipId): Promise<void> {
    await this.prisma.masterChildRelationship.delete({
      where: { id: id.value }
    });
  }
}
