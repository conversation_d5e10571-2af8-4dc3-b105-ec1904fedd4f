import { TradeCopyId } from '../value-objects/TradeCopyId';
import { TradeId } from '../../trading/value-objects/TradeId';
import { UserId } from '../../user/value-objects/UserId';
import { CopyStatus } from '../enums/CopyTradingEnums';

export class TradeCopy {
  private constructor(
    private readonly _id: TradeCopyId,
    private readonly _originalTradeId: TradeId,
    private readonly _masterId: UserId,
    private readonly _childId: UserId,
    private readonly _copiedTradeId: TradeId,
    private _status: CopyStatus,
    private _errorMessage?: string,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date()
  ) {}

  public static create(params: {
    id: TradeCopyId;
    originalTradeId: TradeId;
    masterId: UserId;
    childId: UserId;
    copiedTradeId: TradeId;
  }): TradeCopy {
    return new TradeCopy(
      params.id,
      params.originalTradeId,
      params.masterId,
      params.childId,
      params.copiedTradeId,
      CopyStatus.PENDING
    );
  }

  public static fromPersistence(data: {
    id: string;
    originalTradeId: string;
    masterId: string;
    childId: string;
    copiedTradeId: string;
    status: string;
    errorMessage?: string;
    createdAt: Date;
    updatedAt: Date;
  }): TradeCopy {
    return new TradeCopy(
      TradeCopyId.fromString(data.id),
      TradeId.fromString(data.originalTradeId),
      UserId.fromString(data.masterId),
      UserId.fromString(data.childId),
      TradeId.fromString(data.copiedTradeId),
      data.status as CopyStatus,
      data.errorMessage,
      data.createdAt,
      data.updatedAt
    );
  }

  // Getters
  public get id(): TradeCopyId { return this._id; }
  public get originalTradeId(): TradeId { return this._originalTradeId; }
  public get masterId(): UserId { return this._masterId; }
  public get childId(): UserId { return this._childId; }
  public get copiedTradeId(): TradeId { return this._copiedTradeId; }
  public get status(): CopyStatus { return this._status; }
  public get errorMessage(): string | undefined { return this._errorMessage; }
  public get createdAt(): Date { return this._createdAt; }
  public get updatedAt(): Date { return this._updatedAt; }

  // Business methods
  public markAsCompleted(): void {
    if (this._status !== CopyStatus.PENDING) {
      throw new Error('Can only complete pending trade copies');
    }
    this._status = CopyStatus.COMPLETED;
    this._updatedAt = new Date();
  }

  public markAsFailed(errorMessage: string): void {
    if (this._status === CopyStatus.COMPLETED) {
      throw new Error('Cannot fail a completed trade copy');
    }
    this._status = CopyStatus.FAILED;
    this._errorMessage = errorMessage;
    this._updatedAt = new Date();
  }

  public isPending(): boolean {
    return this._status === CopyStatus.PENDING;
  }

  public isCompleted(): boolean {
    return this._status === CopyStatus.COMPLETED;
  }

  public isFailed(): boolean {
    return this._status === CopyStatus.FAILED;
  }

  public toPersistence(): any {
    return {
      id: this._id.value,
      originalTradeId: this._originalTradeId.value,
      masterId: this._masterId.value,
      childId: this._childId.value,
      copiedTradeId: this._copiedTradeId.value,
      status: this._status,
      errorMessage: this._errorMessage,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    };
  }
}
