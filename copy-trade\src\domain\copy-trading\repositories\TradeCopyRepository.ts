import { TradeCopy } from '../entities/TradeCopy';
import { TradeCopyId } from '../value-objects/TradeCopyId';
import { TradeId } from '../../trading/value-objects/TradeId';
import { UserId } from '../../user/value-objects/UserId';

export interface TradeCopyRepository {
  save(tradeCopy: TradeCopy): Promise<void>;
  findById(id: TradeCopyId): Promise<TradeCopy | null>;
  findByOriginalTradeId(originalTradeId: TradeId): Promise<TradeCopy[]>;
  findByMasterId(masterId: UserId): Promise<TradeCopy[]>;
  findByChildId(childId: UserId): Promise<TradeCopy[]>;
  findPendingCopies(): Promise<TradeCopy[]>;
  update(tradeCopy: TradeCopy): Promise<void>;
  delete(id: TradeCopyId): Promise<void>;
}
