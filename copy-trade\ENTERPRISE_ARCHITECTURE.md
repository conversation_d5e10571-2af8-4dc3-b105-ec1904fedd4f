# Enterprise Copy-Trade Application Architecture

## Overview

This document describes the enterprise-level architecture implementation of the Copy-Trade application, following Domain-Driven Design (DDD) and Hexagonal Architecture patterns inspired by the arthik.ai project structure.

## Architecture Layers

### 1. Domain Layer (`src/domain/`)

The core business logic layer containing entities, value objects, domain services, and business rules.

#### Structure:
```
src/domain/
├── trading/
│   ├── entities/           # Trade entity with business logic
│   ├── value-objects/      # TradeId, Symbol, Price, Quantity
│   ├── repositories/       # TradeRepository interface
│   ├── services/          # TradingDomainService
│   ├── events/            # Domain events (TradeCreated, TradeExecuted)
│   ├── exceptions/        # Domain-specific exceptions
│   └── enums/             # Trading enums (OrderType, TradeStatus)
├── user/
│   ├── entities/          # User entity
│   ├── value-objects/     # UserId, Email
│   ├── repositories/      # UserRepository interface
│   └── enums/             # UserRole, UserStatus
├── copy-trading/
│   ├── entities/          # MasterChildRelationship, TradeCopy
│   ├── value-objects/     # RelationshipId, TradeCopyId
│   ├── repositories/      # Relationship and TradeCopy repositories
│   ├── services/          # CopyTradingDomainService
│   └── enums/             # CopyStatus, RelationshipStatus
└── shared/
    ├── value-objects/     # Money, common value objects
    ├── exceptions/        # Base domain exceptions
    └── types/             # Common domain types
```

#### Key Principles:
- **Entities**: Rich domain objects with business logic and invariants
- **Value Objects**: Immutable objects representing concepts like Price, Quantity
- **Domain Services**: Business logic that doesn't belong to a single entity
- **Repository Interfaces**: Abstract data access without implementation details

### 2. Application Layer (`src/application/`)

Orchestrates domain objects to fulfill use cases and application workflows.

#### Structure:
```
src/application/
├── use-cases/
│   ├── trading/           # CreateTradeUseCase, ExecuteTradeUseCase
│   ├── copy-trading/      # CreateRelationshipUseCase
│   └── user/              # User-related use cases
├── dto/                   # Data Transfer Objects
│   ├── trading/           # CreateTradeDTO, TradeResponseDTO
│   └── copy-trading/      # CreateRelationshipDTO, RelationshipResponseDTO
├── mappers/               # Domain to DTO mapping
│   ├── TradeMapper.ts
│   └── RelationshipMapper.ts
├── validators/            # Input validation
└── events/                # Application event handlers
```

#### Key Principles:
- **Use Cases**: Single responsibility application workflows
- **DTOs**: Data contracts for API communication
- **Mappers**: Transform between domain objects and DTOs
- **No Business Logic**: Pure orchestration of domain objects

### 3. Infrastructure Layer (`src/infrastructure/`)

Implements technical concerns and external integrations.

#### Structure:
```
src/infrastructure/
├── database/
│   └── repositories/      # Prisma repository implementations
│       ├── PrismaTradeRepository.ts
│       ├── PrismaUserRepository.ts
│       └── PrismaMasterChildRelationshipRepository.ts
├── brokers/
│   ├── interfaces/        # BrokerAdapter interface
│   └── zerodha/          # ZerodhaAdapter implementation
├── messaging/             # Event bus, notifications
├── external-apis/         # Third-party API integrations
└── monitoring/            # Logging, metrics, health checks
```

#### Key Principles:
- **Repository Implementations**: Concrete data access using Prisma
- **Adapter Pattern**: Broker integrations with consistent interface
- **Dependency Inversion**: Infrastructure depends on domain interfaces

### 4. Presentation Layer (`src/presentation/`)

User interface components and interaction logic.

#### Structure:
```
src/presentation/
├── components/
│   ├── features/          # Feature-specific components
│   │   ├── trading/       # TradeForm, TradeList
│   │   └── copy-trading/  # RelationshipForm, RelationshipList
│   ├── ui/                # Reusable UI components
│   └── layout/            # Layout components
├── hooks/                 # Custom React hooks
│   ├── useTrading.ts
│   └── useCopyTrading.ts
├── providers/             # Context providers
├── stores/                # State management
└── utils/                 # Presentation utilities
```

#### Key Principles:
- **Feature Organization**: Components grouped by business capability
- **Custom Hooks**: Encapsulate API interactions and state management
- **Separation of Concerns**: UI logic separate from business logic

### 5. Shared Layer (`src/shared/`)

Common utilities, configurations, and cross-cutting concerns.

#### Structure:
```
src/shared/
├── config/                # Configuration management
│   ├── database.ts        # Prisma client setup
│   └── environment.ts     # Environment variables
├── constants/             # Application constants
├── utils/                 # Utility functions
│   ├── logger.ts          # Structured logging
│   └── validation.ts      # Common validation logic
├── container/             # Dependency injection
│   └── Container.ts       # IoC container
└── types/                 # Shared TypeScript types
```

## Key Design Patterns

### 1. Domain-Driven Design (DDD)
- **Ubiquitous Language**: Consistent terminology across all layers
- **Bounded Contexts**: Clear boundaries between trading, user, and copy-trading domains
- **Aggregates**: Trade and User as aggregate roots with consistency boundaries

### 2. Hexagonal Architecture
- **Ports and Adapters**: Domain defines interfaces, infrastructure implements them
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Testability**: Easy to mock external dependencies

### 3. CQRS (Command Query Responsibility Segregation)
- **Commands**: Use cases that modify state (CreateTrade, ExecuteTrade)
- **Queries**: Read operations separated from write operations
- **Event Sourcing**: Domain events capture state changes

### 4. Repository Pattern
- **Abstraction**: Domain defines repository interfaces
- **Implementation**: Infrastructure provides concrete implementations
- **Testability**: Easy to mock for unit testing

## Benefits of This Architecture

### 1. **Maintainability**
- Clear separation of concerns
- Easy to locate and modify specific functionality
- Reduced coupling between layers

### 2. **Testability**
- Domain logic isolated and easily testable
- Infrastructure can be mocked
- Use cases can be tested independently

### 3. **Scalability**
- Modular structure supports team scaling
- Easy to add new features without affecting existing code
- Clear boundaries for microservice extraction

### 4. **Flexibility**
- Easy to swap implementations (database, brokers)
- Technology-agnostic domain layer
- Support for multiple UI frameworks

### 5. **Business Alignment**
- Code structure reflects business domains
- Ubiquitous language improves communication
- Business rules centralized in domain layer

## Migration from Previous Structure

The previous structure had several issues:
- Mixed concerns across layers
- Business logic scattered in multiple places
- Tight coupling between UI and data access
- Difficult to test and maintain

The new structure addresses these issues by:
- Clear layer separation with defined responsibilities
- Business logic centralized in domain entities and services
- Dependency injection for loose coupling
- Comprehensive testing strategy

## Next Steps

1. **Complete Migration**: Move remaining components to new structure
2. **Add Tests**: Implement unit tests for domain logic and use cases
3. **Event System**: Implement domain event publishing and handling
4. **Monitoring**: Add comprehensive logging and metrics
5. **Documentation**: Create API documentation and developer guides

This enterprise architecture provides a solid foundation for scaling the copy-trade application while maintaining code quality and business alignment.
