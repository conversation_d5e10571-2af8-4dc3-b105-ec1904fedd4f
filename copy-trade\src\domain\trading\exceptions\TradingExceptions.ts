export class TradingDomainException extends Error {
  constructor(message: string, public readonly code?: string) {
    super(message);
    this.name = 'TradingDomainException';
  }
}

export class InvalidTradeException extends TradingDomainException {
  constructor(message: string) {
    super(message, 'INVALID_TRADE');
    this.name = 'InvalidTradeException';
  }
}

export class TradeNotFound extends TradingDomainException {
  constructor(tradeId: string) {
    super(`Trade with ID ${tradeId} not found`, 'TRADE_NOT_FOUND');
    this.name = 'TradeNotFound';
  }
}

export class TradeExecutionException extends TradingDomainException {
  constructor(message: string) {
    super(message, 'TRADE_EXECUTION_ERROR');
    this.name = 'TradeExecutionException';
  }
}

export class InsufficientPermissionsException extends TradingDomainException {
  constructor(message: string) {
    super(message, 'INSUFFICIENT_PERMISSIONS');
    this.name = 'InsufficientPermissionsException';
  }
}
