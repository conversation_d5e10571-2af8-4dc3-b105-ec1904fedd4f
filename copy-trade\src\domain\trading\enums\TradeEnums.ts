export enum TradeStatus {
  PENDING = 'PENDING',
  COMPLETE = 'COMPLETE',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED'
}

export enum OrderType {
  MARKET = 'MARKET',
  LIMIT = 'LIMIT',
  SL = 'SL',
  SL_M = 'SL_M'
}

export enum TransactionType {
  BUY = 'BUY',
  SELL = 'SELL'
}

export enum ProductType {
  CNC = 'CNC',
  MIS = 'MIS',
  NRML = 'NRML'
}

export enum Exchange {
  NSE = 'NSE',
  BSE = 'BSE',
  NFO = 'NFO',
  BFO = 'BFO',
  CDS = 'CDS',
  MCX = 'MCX'
}
