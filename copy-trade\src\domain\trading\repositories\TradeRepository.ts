import { Trade } from '../entities/Trade';
import { TradeId } from '../value-objects/TradeId';
import { UserId } from '../../user/value-objects/UserId';

export interface TradeRepository {
  save(trade: Trade): Promise<void>;
  findById(id: TradeId): Promise<Trade | null>;
  findByUserId(userId: UserId): Promise<Trade[]>;
  findPendingTrades(): Promise<Trade[]>;
  findByUserIdAndStatus(userId: UserId, status: string): Promise<Trade[]>;
  update(trade: Trade): Promise<void>;
  delete(id: TradeId): Promise<void>;
}
