export class Quantity {
  private constructor(private readonly _value: number) {
    if (_value <= 0) {
      throw new Error('Quantity must be positive');
    }
    if (!Number.isInteger(_value)) {
      throw new Error('Quantity must be a whole number');
    }
  }

  public static fromNumber(value: number): Quantity {
    return new Quantity(value);
  }

  public get value(): number {
    return this._value;
  }

  public equals(other: Quantity): boolean {
    return this._value === other._value;
  }

  public isGreaterThan(other: Quantity): boolean {
    return this._value > other._value;
  }

  public isLessThan(other: Quantity): boolean {
    return this._value < other._value;
  }

  public add(other: Quantity): Quantity {
    return new Quantity(this._value + other._value);
  }

  public subtract(other: Quantity): Quantity {
    return new Quantity(this._value - other._value);
  }

  public toString(): string {
    return this._value.toString();
  }
}
