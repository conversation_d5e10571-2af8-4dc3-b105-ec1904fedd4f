import { PrismaClient } from '@prisma/client';
import { User } from '../../../domain/user/entities/User';
import { UserRepository } from '../../../domain/user/repositories/UserRepository';
import { UserId } from '../../../domain/user/value-objects/UserId';
import { Email } from '../../../domain/user/value-objects/Email';

export class PrismaUserRepository implements UserRepository {
  constructor(private readonly prisma: PrismaClient) {}

  public async save(user: User): Promise<void> {
    const data = user.toPersistence();
    await this.prisma.user.create({
      data: {
        id: data.id,
        email: data.email,
        name: data.name,
        role: data.role,
        isDemo: data.isDemo,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt
      }
    });
  }

  public async findById(id: UserId): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { id: id.value }
    });

    if (!user) {
      return null;
    }

    return User.fromPersistence({
      id: user.id,
      email: user.email,
      name: user.name || '',
      role: user.role,
      isDemo: user.isDemo,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    });
  }

  public async findByEmail(email: Email): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { email: email.value }
    });

    if (!user) {
      return null;
    }

    return User.fromPersistence({
      id: user.id,
      email: user.email,
      name: user.name || '',
      role: user.role,
      isDemo: user.isDemo,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    });
  }

  public async findAll(): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      orderBy: { createdAt: 'desc' }
    });

    return users.map(user => User.fromPersistence({
      id: user.id,
      email: user.email,
      name: user.name || '',
      role: user.role,
      isDemo: user.isDemo,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }));
  }

  public async update(user: User): Promise<void> {
    const data = user.toPersistence();
    await this.prisma.user.update({
      where: { id: data.id },
      data: {
        name: data.name,
        isDemo: data.isDemo,
        updatedAt: data.updatedAt
      }
    });
  }

  public async delete(id: UserId): Promise<void> {
    await this.prisma.user.delete({
      where: { id: id.value }
    });
  }

  public async existsByEmail(email: Email): Promise<boolean> {
    const count = await this.prisma.user.count({
      where: { email: email.value }
    });
    return count > 0;
  }
}
