import { UserId } from '../value-objects/UserId';
import { Email } from '../value-objects/Email';
import { UserRole } from '../enums/UserEnums';

export class User {
  private constructor(
    private readonly _id: UserId,
    private readonly _email: Email,
    private _name: string,
    private readonly _role: UserRole,
    private _isDemo: boolean = false,
    private readonly _createdAt: Date = new Date(),
    private _updatedAt: Date = new Date()
  ) {}

  public static create(params: {
    id: UserId;
    email: Email;
    name: string;
    role: UserRole;
    isDemo?: boolean;
  }): User {
    return new User(
      params.id,
      params.email,
      params.name,
      params.role,
      params.isDemo || false
    );
  }

  public static fromPersistence(data: {
    id: string;
    email: string;
    name: string;
    role: string;
    isDemo: boolean;
    createdAt: Date;
    updatedAt: Date;
  }): User {
    return new User(
      UserId.fromString(data.id),
      Email.fromString(data.email),
      data.name,
      data.role as UserRole,
      data.isDemo,
      data.createdAt,
      data.updatedAt
    );
  }

  // Getters
  public get id(): UserId { return this._id; }
  public get email(): Email { return this._email; }
  public get name(): string { return this._name; }
  public get role(): UserRole { return this._role; }
  public get isDemo(): boolean { return this._isDemo; }
  public get createdAt(): Date { return this._createdAt; }
  public get updatedAt(): Date { return this._updatedAt; }

  // Business methods
  public updateName(name: string): void {
    if (!name || name.trim().length === 0) {
      throw new Error('Name cannot be empty');
    }
    this._name = name.trim();
    this._updatedAt = new Date();
  }

  public enableDemoMode(): void {
    this._isDemo = true;
    this._updatedAt = new Date();
  }

  public disableDemoMode(): void {
    this._isDemo = false;
    this._updatedAt = new Date();
  }

  public isMaster(): boolean {
    return this._role === UserRole.MASTER;
  }

  public isChild(): boolean {
    return this._role === UserRole.CHILD;
  }

  public canTrade(): boolean {
    // Business rule: Both master and child users can trade
    return this._role === UserRole.MASTER || this._role === UserRole.CHILD;
  }

  public canManageChildren(): boolean {
    // Business rule: Only master users can manage children
    return this._role === UserRole.MASTER;
  }

  public toPersistence(): any {
    return {
      id: this._id.value,
      email: this._email.value,
      name: this._name,
      role: this._role,
      isDemo: this._isDemo,
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    };
  }
}
