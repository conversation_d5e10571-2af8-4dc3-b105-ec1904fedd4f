'use client';

import { RelationshipResponseDTO } from '../../../application/dto/copy-trading/RelationshipResponseDTO';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';
import { Badge } from '../../ui/badge';
import { Button } from '../../ui/button';

interface RelationshipListProps {
  relationships: RelationshipResponseDTO[];
  onDeactivate?: (relationshipId: string) => Promise<void>;
  onActivate?: (relationshipId: string) => Promise<void>;
  onDelete?: (relationshipId: string) => Promise<void>;
  isLoading?: boolean;
  userRole: 'MASTER' | 'CHILD';
}

export function RelationshipList({ 
  relationships, 
  onDeactivate, 
  onActivate, 
  onDelete, 
  isLoading = false,
  userRole 
}: RelationshipListProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  if (relationships.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">
            {userRole === 'MASTER' 
              ? 'No child users connected' 
              : 'Not connected to any master users'
            }
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {relationships.map((relationship) => (
        <Card key={relationship.id}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-lg">
                  {userRole === 'MASTER' 
                    ? `Child User: ${relationship.childId}` 
                    : `Master User: ${relationship.masterId}`
                  }
                </CardTitle>
                <CardDescription>
                  Connected on {formatDate(relationship.createdAt)}
                </CardDescription>
              </div>
              <Badge 
                className={relationship.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
                }
              >
                {relationship.isActive ? 'ACTIVE' : 'INACTIVE'}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Relationship ID</p>
                <p className="font-medium font-mono text-sm">{relationship.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="font-medium">{formatDate(relationship.updatedAt)}</p>
              </div>
            </div>

            <div className="flex space-x-2">
              {relationship.isActive ? (
                onDeactivate && (
                  <Button
                    onClick={() => onDeactivate(relationship.id)}
                    disabled={isLoading}
                    variant="outline"
                    size="sm"
                  >
                    Deactivate
                  </Button>
                )
              ) : (
                onActivate && (
                  <Button
                    onClick={() => onActivate(relationship.id)}
                    disabled={isLoading}
                    size="sm"
                  >
                    Activate
                  </Button>
                )
              )}
              
              {onDelete && (
                <Button
                  onClick={() => onDelete(relationship.id)}
                  disabled={isLoading}
                  variant="destructive"
                  size="sm"
                >
                  Delete
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
