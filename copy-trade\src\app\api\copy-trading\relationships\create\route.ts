import { NextRequest, NextResponse } from 'next/server';
import { CreateRelationshipUseCase } from '../../../../../application/use-cases/copy-trading/CreateRelationshipUseCase';
import { PrismaMasterChildRelationshipRepository } from '../../../../../infrastructure/database/repositories/PrismaMasterChildRelationshipRepository';
import { PrismaUserRepository } from '../../../../../infrastructure/database/repositories/PrismaUserRepository';
import { CopyTradingDomainService } from '../../../../../domain/copy-trading/services/CopyTradingDomainService';
import { PrismaTradeCopyRepository } from '../../../../../infrastructure/database/repositories/PrismaTradeCopyRepository';
import { prisma } from '../../../../../shared/config/database';
import { logger } from '../../../../../shared/utils/logger';
import { Validator } from '../../../../../shared/utils/validation';
import { CreateRelationshipDTO } from '../../../../../application/dto/copy-trading/CreateRelationshipDTO';

export async function POST(request: NextRequest) {
  try {
    const body: CreateRelationshipDTO = await request.json();

    // Validate input
    Validator.validateRequired(body.masterId, 'masterId');
    Validator.validateUUID(body.masterId, 'masterId');
    Validator.validateRequired(body.childId, 'childId');
    Validator.validateUUID(body.childId, 'childId');

    // Initialize repositories and services
    const relationshipRepository = new PrismaMasterChildRelationshipRepository(prisma);
    const userRepository = new PrismaUserRepository(prisma);
    const tradeCopyRepository = new PrismaTradeCopyRepository(prisma);
    const copyTradingDomainService = new CopyTradingDomainService(
      relationshipRepository,
      tradeCopyRepository
    );

    // Initialize use case
    const createRelationshipUseCase = new CreateRelationshipUseCase(
      relationshipRepository,
      userRepository,
      copyTradingDomainService
    );

    // Execute use case
    const result = await createRelationshipUseCase.execute(body);

    logger.info('Relationship created successfully', { 
      relationshipId: result.id,
      masterId: body.masterId,
      childId: body.childId
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    logger.error('Failed to create relationship', error instanceof Error ? error : new Error('Unknown error'));

    if (error instanceof Error) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
