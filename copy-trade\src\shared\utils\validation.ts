import { VALIDATION_CONSTANTS } from '../constants/application';

export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class Validator {
  public static validateEmail(email: string): void {
    if (!email || email.trim().length === 0) {
      throw new ValidationError('Email is required', 'email');
    }

    if (!VALIDATION_CONSTANTS.EMAIL_REGEX.test(email)) {
      throw new ValidationError('Invalid email format', 'email');
    }
  }

  public static validatePassword(password: string): void {
    if (!password || password.length < VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH) {
      throw new ValidationError(
        `Password must be at least ${VALIDATION_CONSTANTS.PASSWORD_MIN_LENGTH} characters long`,
        'password'
      );
    }
  }

  public static validateTradeQuantity(quantity: number): void {
    if (!Number.isInteger(quantity)) {
      throw new ValidationError('Quantity must be a whole number', 'quantity');
    }

    if (quantity < VALIDATION_CONSTANTS.MIN_TRADE_QUANTITY) {
      throw new ValidationError(
        `Quantity must be at least ${VALIDATION_CONSTANTS.MIN_TRADE_QUANTITY}`,
        'quantity'
      );
    }

    if (quantity > VALIDATION_CONSTANTS.MAX_TRADE_QUANTITY) {
      throw new ValidationError(
        `Quantity cannot exceed ${VALIDATION_CONSTANTS.MAX_TRADE_QUANTITY}`,
        'quantity'
      );
    }
  }

  public static validateTradePrice(price: number): void {
    if (!Number.isFinite(price)) {
      throw new ValidationError('Price must be a valid number', 'price');
    }

    if (price < VALIDATION_CONSTANTS.MIN_TRADE_PRICE) {
      throw new ValidationError(
        `Price must be at least ${VALIDATION_CONSTANTS.MIN_TRADE_PRICE}`,
        'price'
      );
    }

    if (price > VALIDATION_CONSTANTS.MAX_TRADE_PRICE) {
      throw new ValidationError(
        `Price cannot exceed ${VALIDATION_CONSTANTS.MAX_TRADE_PRICE}`,
        'price'
      );
    }
  }

  public static validateSymbol(symbol: string): void {
    if (!symbol || symbol.trim().length === 0) {
      throw new ValidationError('Symbol is required', 'symbol');
    }

    if (!VALIDATION_CONSTANTS.SYMBOL_REGEX.test(symbol.toUpperCase())) {
      throw new ValidationError(
        'Symbol must contain only uppercase letters, numbers, underscores, and hyphens',
        'symbol'
      );
    }
  }

  public static validateRequired(value: any, fieldName: string): void {
    if (value === null || value === undefined || value === '') {
      throw new ValidationError(`${fieldName} is required`, fieldName);
    }
  }

  public static validateUUID(value: string, fieldName: string): void {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(value)) {
      throw new ValidationError(`${fieldName} must be a valid UUID`, fieldName);
    }
  }

  public static validateEnum<T extends Record<string, string>>(
    value: string,
    enumObject: T,
    fieldName: string
  ): void {
    const validValues = Object.values(enumObject);
    
    if (!validValues.includes(value)) {
      throw new ValidationError(
        `${fieldName} must be one of: ${validValues.join(', ')}`,
        fieldName
      );
    }
  }
}
