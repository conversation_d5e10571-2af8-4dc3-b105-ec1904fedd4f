import { TradeId } from '../value-objects/TradeId';
import { UserId } from '../../user/value-objects/UserId';

export interface DomainEvent {
  eventId: string;
  occurredOn: Date;
  eventType: string;
}

export class TradeCreatedEvent implements DomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  public readonly eventType = 'TradeCreated';

  constructor(
    public readonly tradeId: TradeId,
    public readonly userId: UserId,
    public readonly symbol: string,
    public readonly transactionType: string,
    public readonly quantity: number,
    public readonly price: number
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}

export class TradeExecutedEvent implements DomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  public readonly eventType = 'TradeExecuted';

  constructor(
    public readonly tradeId: TradeId,
    public readonly userId: UserId,
    public readonly executedAt: Date
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}

export class TradeCancelledEvent implements DomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  public readonly eventType = 'TradeCancelled';

  constructor(
    public readonly tradeId: TradeId,
    public readonly userId: UserId,
    public readonly reason?: string
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}

export class TradeRejectedEvent implements DomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  public readonly eventType = 'TradeRejected';

  constructor(
    public readonly tradeId: TradeId,
    public readonly userId: UserId,
    public readonly reason: string
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}
