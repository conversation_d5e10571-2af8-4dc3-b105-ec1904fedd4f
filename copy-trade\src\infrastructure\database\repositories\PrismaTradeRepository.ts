import { PrismaClient } from '@prisma/client';
import { Trade } from '../../../domain/trading/entities/Trade';
import { TradeRepository } from '../../../domain/trading/repositories/TradeRepository';
import { TradeId } from '../../../domain/trading/value-objects/TradeId';
import { UserId } from '../../../domain/user/value-objects/UserId';

export class PrismaTradeRepository implements TradeRepository {
  constructor(private readonly prisma: PrismaClient) {}

  public async save(trade: Trade): Promise<void> {
    const data = trade.toPersistence();
    await this.prisma.trade.create({
      data: {
        id: data.id,
        userId: data.userId,
        symbol: data.symbol,
        exchange: data.exchange,
        transactionType: data.transactionType,
        quantity: data.quantity,
        price: data.price,
        orderType: data.orderType,
        productType: data.productType,
        status: data.status,
        zerodhaOrderId: data.zerodhaOrderId,
        executedAt: data.executedAt,
        isDemo: data.isDemo,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt
      }
    });
  }

  public async findById(id: TradeId): Promise<Trade | null> {
    const trade = await this.prisma.trade.findUnique({
      where: { id: id.value }
    });

    if (!trade) {
      return null;
    }

    return Trade.fromPersistence({
      id: trade.id,
      userId: trade.userId,
      symbol: trade.symbol,
      exchange: trade.exchange,
      transactionType: trade.transactionType,
      quantity: trade.quantity,
      price: trade.price.toNumber(),
      orderType: trade.orderType,
      productType: trade.productType,
      status: trade.status,
      zerodhaOrderId: trade.zerodhaOrderId || undefined,
      executedAt: trade.executedAt || undefined,
      isDemo: trade.isDemo,
      createdAt: trade.createdAt,
      updatedAt: trade.updatedAt
    });
  }

  public async findByUserId(userId: UserId): Promise<Trade[]> {
    const trades = await this.prisma.trade.findMany({
      where: { userId: userId.value },
      orderBy: { createdAt: 'desc' }
    });

    return trades.map(trade => Trade.fromPersistence({
      id: trade.id,
      userId: trade.userId,
      symbol: trade.symbol,
      exchange: trade.exchange,
      transactionType: trade.transactionType,
      quantity: trade.quantity,
      price: trade.price.toNumber(),
      orderType: trade.orderType,
      productType: trade.productType,
      status: trade.status,
      zerodhaOrderId: trade.zerodhaOrderId || undefined,
      executedAt: trade.executedAt || undefined,
      isDemo: trade.isDemo,
      createdAt: trade.createdAt,
      updatedAt: trade.updatedAt
    }));
  }

  public async findPendingTrades(): Promise<Trade[]> {
    const trades = await this.prisma.trade.findMany({
      where: { status: 'PENDING' },
      orderBy: { createdAt: 'asc' }
    });

    return trades.map(trade => Trade.fromPersistence({
      id: trade.id,
      userId: trade.userId,
      symbol: trade.symbol,
      exchange: trade.exchange,
      transactionType: trade.transactionType,
      quantity: trade.quantity,
      price: trade.price.toNumber(),
      orderType: trade.orderType,
      productType: trade.productType,
      status: trade.status,
      zerodhaOrderId: trade.zerodhaOrderId || undefined,
      executedAt: trade.executedAt || undefined,
      isDemo: trade.isDemo,
      createdAt: trade.createdAt,
      updatedAt: trade.updatedAt
    }));
  }

  public async findByUserIdAndStatus(userId: UserId, status: string): Promise<Trade[]> {
    const trades = await this.prisma.trade.findMany({
      where: { 
        userId: userId.value,
        status: status
      },
      orderBy: { createdAt: 'desc' }
    });

    return trades.map(trade => Trade.fromPersistence({
      id: trade.id,
      userId: trade.userId,
      symbol: trade.symbol,
      exchange: trade.exchange,
      transactionType: trade.transactionType,
      quantity: trade.quantity,
      price: trade.price.toNumber(),
      orderType: trade.orderType,
      productType: trade.productType,
      status: trade.status,
      zerodhaOrderId: trade.zerodhaOrderId || undefined,
      executedAt: trade.executedAt || undefined,
      isDemo: trade.isDemo,
      createdAt: trade.createdAt,
      updatedAt: trade.updatedAt
    }));
  }

  public async update(trade: Trade): Promise<void> {
    const data = trade.toPersistence();
    await this.prisma.trade.update({
      where: { id: data.id },
      data: {
        status: data.status,
        executedAt: data.executedAt,
        updatedAt: data.updatedAt
      }
    });
  }

  public async delete(id: TradeId): Promise<void> {
    await this.prisma.trade.delete({
      where: { id: id.value }
    });
  }
}
