import { v4 as uuidv4 } from 'uuid';

export class TradeCopyId {
  private constructor(private readonly _value: string) {
    if (!_value || _value.trim().length === 0) {
      throw new Error('TradeCopyId cannot be empty');
    }
  }

  public static generate(): TradeCopyId {
    return new TradeCopyId(uuidv4());
  }

  public static fromString(value: string): TradeCopyId {
    return new TradeCopyId(value);
  }

  public get value(): string {
    return this._value;
  }

  public equals(other: TradeCopyId): boolean {
    return this._value === other._value;
  }

  public toString(): string {
    return this._value;
  }
}
