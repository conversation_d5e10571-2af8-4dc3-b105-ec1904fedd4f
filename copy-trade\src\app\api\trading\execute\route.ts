import { NextRequest, NextResponse } from 'next/server';
import { ExecuteTradeUseCase } from '../../../../application/use-cases/trading/ExecuteTradeUseCase';
import { PrismaTradeRepository } from '../../../../infrastructure/database/repositories/PrismaTradeRepository';
import { TradingDomainService } from '../../../../domain/trading/services/TradingDomainService';
import { prisma } from '../../../../shared/config/database';
import { logger } from '../../../../shared/utils/logger';
import { Validator } from '../../../../shared/utils/validation';
import { ExecuteTradeDTO } from '../../../../application/dto/trading/ExecuteTradeDTO';

export async function POST(request: NextRequest) {
  try {
    const body: ExecuteTradeDTO = await request.json();

    // Validate input
    Validator.validateRequired(body.tradeId, 'tradeId');
    Validator.validateUUID(body.tradeId, 'tradeId');
    Validator.validateRequired(body.userId, 'userId');
    Validator.validateUUID(body.userId, 'userId');

    // Initialize repositories and services
    const tradeRepository = new PrismaTradeRepository(prisma);
    const tradingDomainService = new TradingDomainService(tradeRepository);

    // Initialize use case
    const executeTradeUseCase = new ExecuteTradeUseCase(
      tradeRepository,
      tradingDomainService
    );

    // Execute use case
    const result = await executeTradeUseCase.execute(body);

    logger.info('Trade executed successfully', { 
      tradeId: body.tradeId, 
      userId: body.userId 
    });

    return NextResponse.json(result);
  } catch (error) {
    logger.error('Failed to execute trade', error instanceof Error ? error : new Error('Unknown error'));

    if (error instanceof Error) {
      return NextResponse.json(
        { message: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
