import { PrismaClient } from '@prisma/client';

// Domain Services
import { TradingDomainService } from '../../domain/trading/services/TradingDomainService';
import { CopyTradingDomainService } from '../../domain/copy-trading/services/CopyTradingDomainService';

// Repositories
import { TradeRepository } from '../../domain/trading/repositories/TradeRepository';
import { UserRepository } from '../../domain/user/repositories/UserRepository';
import { MasterChildRelationshipRepository } from '../../domain/copy-trading/repositories/MasterChildRelationshipRepository';
import { TradeCopyRepository } from '../../domain/copy-trading/repositories/TradeCopyRepository';

// Infrastructure
import { PrismaTradeRepository } from '../../infrastructure/database/repositories/PrismaTradeRepository';
import { PrismaUserRepository } from '../../infrastructure/database/repositories/PrismaUserRepository';
import { PrismaMasterChildRelationshipRepository } from '../../infrastructure/database/repositories/PrismaMasterChildRelationshipRepository';
import { PrismaTradeCopyRepository } from '../../infrastructure/database/repositories/PrismaTradeCopyRepository';

// Use Cases
import { CreateTradeUseCase } from '../../application/use-cases/trading/CreateTradeUseCase';
import { ExecuteTradeUseCase } from '../../application/use-cases/trading/ExecuteTradeUseCase';
import { CreateRelationshipUseCase } from '../../application/use-cases/copy-trading/CreateRelationshipUseCase';

// Configuration
import { prisma } from '../config/database';

export class Container {
  private static instance: Container;
  private readonly _prisma: PrismaClient;

  // Repositories
  private _tradeRepository?: TradeRepository;
  private _userRepository?: UserRepository;
  private _relationshipRepository?: MasterChildRelationshipRepository;
  private _tradeCopyRepository?: TradeCopyRepository;

  // Domain Services
  private _tradingDomainService?: TradingDomainService;
  private _copyTradingDomainService?: CopyTradingDomainService;

  // Use Cases
  private _createTradeUseCase?: CreateTradeUseCase;
  private _executeTradeUseCase?: ExecuteTradeUseCase;
  private _createRelationshipUseCase?: CreateRelationshipUseCase;

  private constructor() {
    this._prisma = prisma;
  }

  public static getInstance(): Container {
    if (!Container.instance) {
      Container.instance = new Container();
    }
    return Container.instance;
  }

  // Repository getters
  public get tradeRepository(): TradeRepository {
    if (!this._tradeRepository) {
      this._tradeRepository = new PrismaTradeRepository(this._prisma);
    }
    return this._tradeRepository;
  }

  public get userRepository(): UserRepository {
    if (!this._userRepository) {
      this._userRepository = new PrismaUserRepository(this._prisma);
    }
    return this._userRepository;
  }

  public get relationshipRepository(): MasterChildRelationshipRepository {
    if (!this._relationshipRepository) {
      this._relationshipRepository = new PrismaMasterChildRelationshipRepository(this._prisma);
    }
    return this._relationshipRepository;
  }

  public get tradeCopyRepository(): TradeCopyRepository {
    if (!this._tradeCopyRepository) {
      this._tradeCopyRepository = new PrismaTradeCopyRepository(this._prisma);
    }
    return this._tradeCopyRepository;
  }

  // Domain Service getters
  public get tradingDomainService(): TradingDomainService {
    if (!this._tradingDomainService) {
      this._tradingDomainService = new TradingDomainService(this.tradeRepository);
    }
    return this._tradingDomainService;
  }

  public get copyTradingDomainService(): CopyTradingDomainService {
    if (!this._copyTradingDomainService) {
      this._copyTradingDomainService = new CopyTradingDomainService(
        this.relationshipRepository,
        this.tradeCopyRepository
      );
    }
    return this._copyTradingDomainService;
  }

  // Use Case getters
  public get createTradeUseCase(): CreateTradeUseCase {
    if (!this._createTradeUseCase) {
      this._createTradeUseCase = new CreateTradeUseCase(
        this.tradeRepository,
        this.userRepository,
        this.tradingDomainService
      );
    }
    return this._createTradeUseCase;
  }

  public get executeTradeUseCase(): ExecuteTradeUseCase {
    if (!this._executeTradeUseCase) {
      this._executeTradeUseCase = new ExecuteTradeUseCase(
        this.tradeRepository,
        this.tradingDomainService
      );
    }
    return this._executeTradeUseCase;
  }

  public get createRelationshipUseCase(): CreateRelationshipUseCase {
    if (!this._createRelationshipUseCase) {
      this._createRelationshipUseCase = new CreateRelationshipUseCase(
        this.relationshipRepository,
        this.userRepository,
        this.copyTradingDomainService
      );
    }
    return this._createRelationshipUseCase;
  }

  // Cleanup method for testing
  public async cleanup(): Promise<void> {
    await this._prisma.$disconnect();
  }
}

// Export singleton instance
export const container = Container.getInstance();
