'use client';

import { useState, useCallback } from 'react';
import { CreateRelationshipDTO } from '../../application/dto/copy-trading/CreateRelationshipDTO';
import { RelationshipResponseDTO } from '../../application/dto/copy-trading/RelationshipResponseDTO';

export function useCopyTrading() {
  const [relationships, setRelationships] = useState<RelationshipResponseDTO[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createRelationship = useCallback(async (relationshipData: CreateRelationshipDTO): Promise<RelationshipResponseDTO> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/copy-trading/relationships/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(relationshipData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create relationship');
      }

      const relationship = await response.json();
      setRelationships(prev => [relationship, ...prev]);
      return relationship;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const activateRelationship = useCallback(async (relationshipId: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/copy-trading/relationships/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ relationshipId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to activate relationship');
      }

      const updatedRelationship = await response.json();
      setRelationships(prev => prev.map(rel => 
        rel.id === updatedRelationship.id ? updatedRelationship : rel
      ));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deactivateRelationship = useCallback(async (relationshipId: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/copy-trading/relationships/deactivate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ relationshipId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to deactivate relationship');
      }

      const updatedRelationship = await response.json();
      setRelationships(prev => prev.map(rel => 
        rel.id === updatedRelationship.id ? updatedRelationship : rel
      ));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteRelationship = useCallback(async (relationshipId: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/copy-trading/relationships/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ relationshipId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete relationship');
      }

      setRelationships(prev => prev.filter(rel => rel.id !== relationshipId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchRelationships = useCallback(async (userId: string, userRole: 'MASTER' | 'CHILD'): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const endpoint = userRole === 'MASTER' 
        ? `/api/copy-trading/relationships/master?userId=${userId}`
        : `/api/copy-trading/relationships/child?userId=${userId}`;
        
      const response = await fetch(endpoint);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch relationships');
      }

      const relationshipsData = await response.json();
      setRelationships(relationshipsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    relationships,
    isLoading,
    error,
    createRelationship,
    activateRelationship,
    deactivateRelationship,
    deleteRelationship,
    fetchRelationships,
    clearError,
  };
}
