import { Trade } from '../../../domain/trading/entities/Trade';
import { BrokerAdapter } from '../interfaces/BrokerAdapter';

export interface ZerodhaTradeParams {
  tradingsymbol: string;
  exchange: string;
  transaction_type: 'BUY' | 'SELL';
  quantity: number;
  price?: number;
  order_type: 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
  product: 'CNC' | 'MIS' | 'NRML';
  validity?: 'DAY' | 'IOC';
  disclosed_quantity?: number;
  trigger_price?: number;
  squareoff?: number;
  stoploss?: number;
  trailing_stoploss?: number;
  tag?: string;
}

export interface ZerodhaOrderResponse {
  status: 'success' | 'error';
  data?: {
    order_id: string;
  };
  message?: string;
  error_type?: string;
}

export class ZerodhaAdapter implements BrokerAdapter {
  constructor(
    private readonly apiKey: string,
    private readonly accessToken: string
  ) {}

  public async placeTrade(trade: Trade): Promise<string> {
    if (trade.isDemo) {
      // For demo mode, return a mock order ID
      return `DEMO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    const params: ZerodhaTradeParams = {
      tradingsymbol: trade.symbol.value,
      exchange: trade.exchange,
      transaction_type: trade.transactionType,
      quantity: trade.quantity.value,
      order_type: this.mapOrderType(trade.orderType),
      product: trade.productType,
      validity: 'DAY'
    };

    // Add price for limit orders
    if (trade.orderType === 'LIMIT') {
      params.price = trade.price.value;
    }

    try {
      const response = await this.makeApiCall('/orders/regular', 'POST', params);
      
      if (response.status === 'success' && response.data?.order_id) {
        return response.data.order_id;
      } else {
        throw new Error(response.message || 'Failed to place order');
      }
    } catch (error) {
      throw new Error(`Zerodha API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async getOrderStatus(orderId: string): Promise<string> {
    if (orderId.startsWith('DEMO_')) {
      // For demo orders, return COMPLETE status
      return 'COMPLETE';
    }

    try {
      const response = await this.makeApiCall(`/orders/${orderId}`, 'GET');
      
      if (response.status === 'success' && response.data) {
        return this.mapOrderStatus(response.data.status);
      } else {
        throw new Error(response.message || 'Failed to get order status');
      }
    } catch (error) {
      throw new Error(`Zerodha API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async cancelOrder(orderId: string): Promise<boolean> {
    if (orderId.startsWith('DEMO_')) {
      // For demo orders, always return success
      return true;
    }

    try {
      const response = await this.makeApiCall(`/orders/regular/${orderId}`, 'DELETE');
      return response.status === 'success';
    } catch (error) {
      throw new Error(`Zerodha API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async makeApiCall(endpoint: string, method: string, data?: any): Promise<ZerodhaOrderResponse> {
    const url = `https://api.kite.trade${endpoint}`;
    
    const headers: Record<string, string> = {
      'Authorization': `token ${this.apiKey}:${this.accessToken}`,
      'Content-Type': 'application/x-www-form-urlencoded'
    };

    const options: RequestInit = {
      method,
      headers
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      const formData = new URLSearchParams();
      Object.keys(data).forEach(key => {
        formData.append(key, data[key].toString());
      });
      options.body = formData;
    }

    const response = await fetch(url, options);
    return await response.json();
  }

  private mapOrderType(orderType: string): 'MARKET' | 'LIMIT' | 'SL' | 'SL-M' {
    switch (orderType) {
      case 'SL_M':
        return 'SL-M';
      default:
        return orderType as 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
    }
  }

  private mapOrderStatus(zerodhaStatus: string): string {
    switch (zerodhaStatus.toUpperCase()) {
      case 'COMPLETE':
        return 'COMPLETE';
      case 'CANCELLED':
        return 'CANCELLED';
      case 'REJECTED':
        return 'REJECTED';
      case 'OPEN':
      case 'TRIGGER PENDING':
        return 'PENDING';
      default:
        return 'PENDING';
    }
  }
}
