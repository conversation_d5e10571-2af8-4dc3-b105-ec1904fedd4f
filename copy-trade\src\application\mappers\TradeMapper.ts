import { Trade } from '../../domain/trading/entities/Trade';
import { TradeResponseDTO } from '../dto/trading/TradeResponseDTO';

export class TradeMapper {
  public static toResponseDTO(trade: Trade): TradeResponseDTO {
    return {
      id: trade.id.value,
      userId: trade.userId.value,
      symbol: trade.symbol.value,
      exchange: trade.exchange,
      transactionType: trade.transactionType,
      quantity: trade.quantity.value,
      price: trade.price.value,
      orderType: trade.orderType,
      productType: trade.productType,
      status: trade.status,
      zerodhaOrderId: trade.zerodhaOrderId,
      executedAt: trade.executedAt,
      isDemo: trade.isDemo,
      createdAt: trade.createdAt,
      updatedAt: trade.updatedAt
    };
  }

  public static toResponseDTOList(trades: Trade[]): TradeResponseDTO[] {
    return trades.map(trade => this.toResponseDTO(trade));
  }
}
