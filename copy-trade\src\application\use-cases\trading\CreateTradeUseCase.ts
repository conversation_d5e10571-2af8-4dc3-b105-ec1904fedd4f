import { Trade } from '../../../domain/trading/entities/Trade';
import { TradeRepository } from '../../../domain/trading/repositories/TradeRepository';
import { UserRepository } from '../../../domain/user/repositories/UserRepository';
import { TradingDomainService } from '../../../domain/trading/services/TradingDomainService';
import { TradeId } from '../../../domain/trading/value-objects/TradeId';
import { UserId } from '../../../domain/user/value-objects/UserId';
import { Symbol } from '../../../domain/trading/value-objects/Symbol';
import { Price } from '../../../domain/trading/value-objects/Price';
import { Quantity } from '../../../domain/trading/value-objects/Quantity';
import { TransactionType, OrderType, ProductType } from '../../../domain/trading/enums/TradeEnums';
import { CreateTradeDTO } from '../../dto/trading/CreateTradeDTO';
import { TradeResponseDTO } from '../../dto/trading/TradeResponseDTO';
import { TradeMapper } from '../../mappers/TradeMapper';

export class CreateTradeUseCase {
  constructor(
    private readonly tradeRepository: TradeRepository,
    private readonly userRepository: UserRepository,
    private readonly tradingDomainService: TradingDomainService
  ) {}

  public async execute(dto: CreateTradeDTO): Promise<TradeResponseDTO> {
    // Validate user exists
    const userId = UserId.fromString(dto.userId);
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Validate user can trade
    if (!user.canTrade()) {
      throw new Error('User is not authorized to trade');
    }

    // Create trade entity
    const trade = Trade.create({
      id: TradeId.generate(),
      userId: userId,
      symbol: Symbol.fromString(dto.symbol),
      exchange: dto.exchange,
      transactionType: dto.transactionType as TransactionType,
      quantity: Quantity.fromNumber(dto.quantity),
      price: Price.fromNumber(dto.price),
      orderType: dto.orderType as OrderType,
      productType: dto.productType as ProductType,
      zerodhaOrderId: dto.zerodhaOrderId,
      isDemo: dto.isDemo || false
    });

    // Validate trade can be executed
    const canExecute = await this.tradingDomainService.canExecuteTrade(trade);
    if (!canExecute) {
      throw new Error('Trade cannot be executed');
    }

    // Save trade
    await this.tradeRepository.save(trade);

    // Return response
    return TradeMapper.toResponseDTO(trade);
  }
}
